name: busaty_parents
description: A new Flutter project for Busaty Parents
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.5.0+42

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Firebase
  firebase_core: ^3.9.0
  firebase_auth: ^5.3.4
  firebase_messaging: ^15.2.5
  google_sign_in: ^6.2.2

  # Local Notifications
  flutter_local_notifications: ^18.0.1

  # For timezone support in notifications
  timezone: 0.10.0

  # For content hashing in notification deduplication
  crypto: ^3.0.3

  # For dependency injection
  cupertino_icons: ^1.0.8
  shared_preferences: 2.3.4
  flutter_screenutil: ^5.9.3
  easy_localization: ^3.0.7
  easy_localization_loader: ^2.0.2  # Updated version
  shimmer: 3.0.0
  share_plus: ^7.2.1
  cached_network_image: 3.4.1
  flutter_rating_bar: ^4.0.1
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6
  pin_code_fields: 8.0.1
  flutter_svg: 2.0.16
  image_picker: 1.1.2
  equatable: ^2.0.7
  badges: ^3.1.2
  file_picker: 8.1.7
  svg_path_parser: ^1.1.2
  touchable: ^1.0.2
  dio: ^5.7.0
  mqtt_client:
  in_app_purchase:
  logger: 2.5.0
  flutter_datetime_picker_plus: ^2.2.0
  geocoding: 3.0.0
  geolocator: ^10.1.0
  google_maps_flutter: 2.10.0
  country_code_picker: ^3.1.0
  flutter_osm_plugin:
  socket_io_client:
  open_filex: ^4.7.0
  flutter_inapp_purchase: 5.6.2
  google_mobile_ads: 5.2.0
  get_it: 8.0.3
  carousel_slider: 5.0.0
  extended_image: 9.0.9
  url_launcher: ^6.2.5
  open_street_map_search_and_pick: 0.1.1
  app_links:
  audioplayers:
  flutter_isolate: ^2.1.0
  internet_connection_checker: ^3.0.1
  g_recaptcha_v3: ^1.0.0
  mailer: ^6.2.3
  # huawei_push:
  # pay: ^1.1.0
  # huawei_push: ^6.11.0+300
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
  assets:
    - assets/images/Frame.svg
    - assets/images/all_driver_icon.svg
    - assets/images/ar.png
    - assets/images/back-arrow.svg
    - assets/images/bg.png
    - assets/images/bus_home_icon.svg
    - assets/images/disconnect_internet.png
    - assets/images/done.png
    - assets/images/driver_home_icon.svg
    - assets/images/forward-arrow.svg
    - assets/images/gb.png
    - assets/images/group1.png
    - assets/images/groups.svg
    - assets/images/home1.png
    - assets/images/icon.png
    - assets/images/iconLogo.png
    - assets/images/location.png
    - assets/images/location_icon.svg
    - assets/images/logo.png
    - assets/images/marker_home_1.png
    - assets/images/marker_one.png
    - assets/images/marker_school_1.png
    - assets/images/marker_two.png
    - assets/images/palestine-flag-icon.png
    - assets/images/parint_icon.svg
    - assets/images/pc.png
    - assets/images/school1.png
    - assets/images/setting.png
    - assets/images/supervisor_home_icon.svg
    - assets/images/supervisor_icon.svg
    - assets/images/apple_pay_logo.png
    - assets/images/google_icon.png
    - assets/sound/msb_bus_horn_text.mp3
    - assets/sound/ring_low.mp3
    - assets/translations/
    - assets/translations/en.json
    - assets/translations/ar.json
