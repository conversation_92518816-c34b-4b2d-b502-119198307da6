import 'package:flutter_test/flutter_test.dart';
import 'package:busaty_parents/notification_service/utils/content_hasher.dart';
import 'package:busaty_parents/notification_service/models/notification_data.dart';

void main() {
  group('Rebuilt Notification System - Content-Based Deduplication', () {
    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('should generate consistent IDs for identical content', () {
      final notification1 = NotificationData(
        id: '',
        title: 'Good Evening',
        body: 'Test notification',
        data: {'type': 'test'},
        receivedAt: DateTime.now(),
        firebaseMessageId: '0:1749984838088366%ab99f1c5ab99f1c5',
      );

      final notification2 = NotificationData(
        id: '',
        title: 'Good Evening',
        body: 'Test notification',
        data: {'type': 'test'},
        receivedAt: DateTime.now().add(Duration(seconds: 10)), // Different time
        firebaseMessageId: '0:1749984846150743%ab99f1c5ab99f1c5', // Different Firebase ID
      );

      final id1 = ContentHasher.generateNotificationId(notification1);
      final id2 = ContentHasher.generateNotificationId(notification2);

      expect(id1, equals(id2), reason: 'Identical content should generate same ID');
      expect(id1, isNotEmpty);
      expect(id2, isNotEmpty);
    });

    test('should generate different IDs for different content', () {
      final notification1 = NotificationData(
        id: '',
        title: 'Good Evening',
        body: 'Test notification',
        data: {'type': 'test'},
        receivedAt: DateTime.now(),
      );

      final notification2 = NotificationData(
        id: '',
        title: 'Good Morning', // Different title
        body: 'Test notification',
        data: {'type': 'test'},
        receivedAt: DateTime.now(),
      );

      final id1 = ContentHasher.generateNotificationId(notification1);
      final id2 = ContentHasher.generateNotificationId(notification2);

      expect(id1, isNot(equals(id2)), reason: 'Different content should generate different IDs');
    });

    test('should ignore timestamp-based data fields', () {
      final notification1 = NotificationData(
        id: '',
        title: 'Test',
        body: 'Body',
        data: {
          'message': 'content',
          'timestamp': '1234567890',
          'sent_time': '2023-01-01',
          'id': 'some_id',
        },
        receivedAt: DateTime.now(),
      );

      final notification2 = NotificationData(
        id: '',
        title: 'Test',
        body: 'Body',
        data: {
          'message': 'content',
          'timestamp': '9876543210', // Different timestamp
          'sent_time': '2023-01-02', // Different time
          'id': 'different_id', // Different ID
        },
        receivedAt: DateTime.now(),
      );

      final id1 = ContentHasher.generateNotificationId(notification1);
      final id2 = ContentHasher.generateNotificationId(notification2);

      expect(id1, equals(id2), reason: 'Should ignore timestamp-based fields');
    });

    test('YOUR EXACT SCENARIO: Server-side duplicates with different Firebase IDs', () {
      // This test simulates your exact scenario from the logs
      final notification1 = NotificationData(
        id: '',
        title: 'Good Evening',
        body: 'Test notification',
        data: {},
        receivedAt: DateTime.fromMillisecondsSinceEpoch(1749984838088),
        firebaseMessageId: '0:1749984838088366%ab99f1c5ab99f1c5',
      );

      final notification2 = NotificationData(
        id: '',
        title: 'Good Evening', // Same content
        body: 'Test notification', // Same content
        data: {},
        receivedAt: DateTime.fromMillisecondsSinceEpoch(1749984846150), // Different time
        firebaseMessageId: '0:1749984846150743%ab99f1c5ab99f1c5', // Different Firebase ID
      );

      // Generate content-based IDs
      final id1 = ContentHasher.generateNotificationId(notification1);
      final id2 = ContentHasher.generateNotificationId(notification2);

      // Should generate same ID for same content
      expect(id1, equals(id2), reason: 'Same content should generate same ID');
      
      // Verify the IDs are not empty
      expect(id1, isNotEmpty);
      expect(id2, isNotEmpty);
      
      // Verify consistent hashing
      expect(ContentHasher.haveSameContent(notification1, notification2), isTrue);
      
      print('✅ SUCCESS: Server-side duplicates detected!');
      print('   Firebase ID 1: ${notification1.firebaseMessageId}');
      print('   Firebase ID 2: ${notification2.firebaseMessageId}');
      print('   Generated same content-based ID: $id1');
    });

    test('should validate hash consistency', () {
      final notification = NotificationData(
        id: '',
        title: 'Consistency Test',
        body: 'Testing hash consistency',
        data: {'stable': 'data'},
        receivedAt: DateTime.now(),
      );

      // Test that multiple calls generate the same ID
      final id1 = ContentHasher.generateNotificationId(notification);
      final id2 = ContentHasher.generateNotificationId(notification);
      final id3 = ContentHasher.generateNotificationId(notification);

      expect(id1, equals(id2));
      expect(id2, equals(id3));
      expect(ContentHasher.validateHashingConsistency(notification), isTrue);
    });

    test('should handle empty content gracefully', () {
      final notification = NotificationData(
        id: '',
        title: '',
        body: '',
        data: {},
        receivedAt: DateTime.now(),
      );

      final id = ContentHasher.generateNotificationId(notification);
      expect(id, isNotEmpty, reason: 'Should generate fallback ID for empty content');
    });

    test('should extract stable data correctly', () {
      final testData = {
        'message': 'stable content',
        'type': 'notification',
        'timestamp': '1234567890', // Should be removed
        'sent_time': '2023-01-01', // Should be removed
        'id': 'some_id', // Should be removed
        'uuid': 'some-uuid', // Should be removed
        'created_at': '2023-01-01', // Should be removed
      };

      final stableData = ContentHasher.extractStableData(testData);

      expect(stableData.containsKey('message'), isTrue);
      expect(stableData.containsKey('type'), isTrue);
      expect(stableData.containsKey('timestamp'), isFalse);
      expect(stableData.containsKey('sent_time'), isFalse);
      expect(stableData.containsKey('id'), isFalse);
      expect(stableData.containsKey('uuid'), isFalse);
      expect(stableData.containsKey('created_at'), isFalse);
    });
  });
}
