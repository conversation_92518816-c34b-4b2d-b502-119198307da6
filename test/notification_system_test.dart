import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:busaty_parents/notification_service/utils/unified_notification_deduplication.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Notification System Tests', () {
    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      await CacheHelper.init();
      
      // Clear all notification history before each test
      await UnifiedNotificationDeduplication.clearAllHistory();
    });

    group('Unified Notification Deduplication', () {
      test('should allow first notification', () async {
        const messageId = 'test_message_1';
        
        final shouldShow = await UnifiedNotificationDeduplication.shouldShowNotification(messageId);
        
        expect(shouldShow, isTrue);
      });

      test('should block duplicate notification within window', () async {
        const messageId = 'test_message_2';
        
        // First notification should be allowed
        final firstShow = await UnifiedNotificationDeduplication.shouldShowNotification(messageId);
        expect(firstShow, isTrue);
        
        // Immediate duplicate should be blocked
        final secondShow = await UnifiedNotificationDeduplication.shouldShowNotification(messageId);
        expect(secondShow, isFalse);
      });

      test('should allow notification after expiry', () async {
        const messageId = 'test_message_3';
        
        // First notification
        final firstShow = await UnifiedNotificationDeduplication.shouldShowNotification(messageId);
        expect(firstShow, isTrue);
        
        // Simulate time passing by manually clearing the specific entry
        await CacheHelper.remove('unified_notification_$messageId');
        await CacheHelper.remove('unified_notification_expiry_$messageId');
        
        // Should allow after expiry
        final afterExpiry = await UnifiedNotificationDeduplication.shouldShowNotification(messageId);
        expect(afterExpiry, isTrue);
      });

      test('should handle multiple different notifications', () async {
        const messageId1 = 'test_message_4a';
        const messageId2 = 'test_message_4b';
        const messageId3 = 'test_message_4c';
        
        // All different notifications should be allowed
        final show1 = await UnifiedNotificationDeduplication.shouldShowNotification(messageId1);
        final show2 = await UnifiedNotificationDeduplication.shouldShowNotification(messageId2);
        final show3 = await UnifiedNotificationDeduplication.shouldShowNotification(messageId3);
        
        expect(show1, isTrue);
        expect(show2, isTrue);
        expect(show3, isTrue);
        
        // Duplicates should be blocked
        final duplicate1 = await UnifiedNotificationDeduplication.shouldShowNotification(messageId1);
        final duplicate2 = await UnifiedNotificationDeduplication.shouldShowNotification(messageId2);
        
        expect(duplicate1, isFalse);
        expect(duplicate2, isFalse);
      });

      test('should provide correct cache statistics', () async {
        const messageId1 = 'stats_test_1';
        const messageId2 = 'stats_test_2';
        
        // Add some notifications
        await UnifiedNotificationDeduplication.shouldShowNotification(messageId1);
        await UnifiedNotificationDeduplication.shouldShowNotification(messageId2);
        
        final stats = UnifiedNotificationDeduplication.getCacheStats();
        
        expect(stats['memoryCount'], greaterThan(0));
        expect(stats['duplicateWindowMinutes'], equals(10));
        expect(stats['persistentWindowHours'], equals(24));
      });

      test('should cleanup expired entries', () async {
        const messageId = 'cleanup_test';
        
        // Add a notification
        await UnifiedNotificationDeduplication.shouldShowNotification(messageId);
        
        // Manually set expiry to past time
        final pastTime = DateTime.now().subtract(Duration(hours: 25)).millisecondsSinceEpoch;
        await CacheHelper.putInt('unified_notification_expiry_$messageId', pastTime);
        
        // Run cleanup
        await UnifiedNotificationDeduplication.cleanupExpiredEntries();
        
        // Should allow notification again after cleanup
        final shouldShow = await UnifiedNotificationDeduplication.shouldShowNotification(messageId);
        expect(shouldShow, isTrue);
      });
    });

    group('Message ID Generation', () {
      test('should generate consistent IDs for same message', () {
        // Create mock RemoteMessage
        final message1 = _createMockMessage(
          messageId: 'firebase_123',
          title: 'Test Title',
          body: 'Test Body',
          data: {'key': 'value'},
        );
        
        final message2 = _createMockMessage(
          messageId: 'firebase_123',
          title: 'Test Title',
          body: 'Test Body',
          data: {'key': 'value'},
        );
        
        final id1 = _generateConsistentMessageId(message1);
        final id2 = _generateConsistentMessageId(message2);
        
        expect(id1, equals(id2));
      });

      test('should generate different IDs for different messages', () {
        final message1 = _createMockMessage(
          messageId: 'firebase_123',
          title: 'Test Title 1',
          body: 'Test Body 1',
        );
        
        final message2 = _createMockMessage(
          messageId: 'firebase_456',
          title: 'Test Title 2',
          body: 'Test Body 2',
        );
        
        final id1 = _generateConsistentMessageId(message1);
        final id2 = _generateConsistentMessageId(message2);
        
        expect(id1, isNot(equals(id2)));
      });

      test('should handle missing message ID', () {
        final message = _createMockMessage(
          messageId: null,
          title: 'Test Title',
          body: 'Test Body',
          sentTime: DateTime.now(),
        );
        
        final id = _generateConsistentMessageId(message);
        
        expect(id, isNotNull);
        expect(id, isNotEmpty);
      });
    });
  });
}

// Helper function to generate consistent message IDs (copied from implementation)
String _generateConsistentMessageId(RemoteMessage message) {
  if (message.messageId != null && message.messageId!.isNotEmpty) {
    return message.messageId!;
  }
  
  // If no message ID, create a deterministic ID based on message content
  final String contentHash = '${message.notification?.title ?? ''}'
      '${message.notification?.body ?? ''}'
      '${message.data.toString()}'
      '${message.sentTime?.millisecondsSinceEpoch ?? 0}';
  
  return contentHash.hashCode.toString();
}

// Helper function to create mock RemoteMessage for testing
RemoteMessage _createMockMessage({
  String? messageId,
  String? title,
  String? body,
  Map<String, dynamic>? data,
  DateTime? sentTime,
}) {
  return RemoteMessage(
    messageId: messageId,
    notification: RemoteNotification(
      title: title,
      body: body,
    ),
    data: data ?? {},
    sentTime: sentTime,
  );
}
