import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:busaty_parents/notification_service/utils/universal_notification_deduplication.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Notification System Tests', () {
    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      await CacheHelper.init();

      // Clear all notification history before each test
      await UniversalNotificationDeduplication.clearAll();
    });

    group('Universal Notification Deduplication', () {
      test('should allow first notification', () async {
        const messageId = 'test_message_1';

        final shouldShow =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId);

        expect(shouldShow, isTrue);
      });

      test('should block duplicate notification within window', () async {
        const messageId = 'test_message_2';

        // First notification should be allowed
        final firstShow =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId);
        expect(firstShow, isTrue);

        // Immediate duplicate should be blocked
        final secondShow =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId);
        expect(secondShow, isFalse);
      });

      test('should allow notification after expiry', () async {
        const messageId = 'test_message_3';

        // First notification
        final firstShow =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId);
        expect(firstShow, isTrue);

        // Simulate time passing by manually clearing the specific entry
        await CacheHelper.remove('universal_notif_$messageId');
        await CacheHelper.remove('universal_notif_exp_$messageId');

        // Should allow after expiry
        final afterExpiry =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId);
        expect(afterExpiry, isTrue);
      });

      test('should handle multiple different notifications', () async {
        const messageId1 = 'test_message_4a';
        const messageId2 = 'test_message_4b';
        const messageId3 = 'test_message_4c';

        // All different notifications should be allowed
        final show1 =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId1);
        final show2 =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId2);
        final show3 =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId3);

        expect(show1, isTrue);
        expect(show2, isTrue);
        expect(show3, isTrue);

        // Duplicates should be blocked
        final duplicate1 =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId1);
        final duplicate2 =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId2);

        expect(duplicate1, isFalse);
        expect(duplicate2, isFalse);
      });

      test('should provide correct cache statistics', () async {
        const messageId1 = 'stats_test_1';
        const messageId2 = 'stats_test_2';

        // Add some notifications
        await UniversalNotificationDeduplication.shouldShowNotification(
            messageId1);
        await UniversalNotificationDeduplication.shouldShowNotification(
            messageId2);

        final stats = await UniversalNotificationDeduplication.getStats();

        expect(stats['active_entries'], greaterThan(0));
        expect(stats['duplicate_window_minutes'], equals(20));
        expect(stats['system_type'], equals('universal'));
      });

      test('should cleanup expired entries', () async {
        const messageId = 'cleanup_test';

        // Add a notification
        await UniversalNotificationDeduplication.shouldShowNotification(
            messageId);

        // Manually set expiry to past time
        final pastTime =
            DateTime.now().subtract(Duration(hours: 25)).millisecondsSinceEpoch;
        await CacheHelper.putInt('universal_notif_exp_$messageId', pastTime);

        // Run cleanup
        await UniversalNotificationDeduplication.cleanupExpiredEntries();

        // Should allow notification again after cleanup
        final shouldShow =
            await UniversalNotificationDeduplication.shouldShowNotification(
                messageId);
        expect(shouldShow, isTrue);
      });
    });

    group('Message ID Generation', () {
      test('should generate consistent IDs for same message', () {
        // Create mock RemoteMessage
        final message1 = _createMockMessage(
          messageId: 'firebase_123',
          title: 'Test Title',
          body: 'Test Body',
          data: {'key': 'value'},
        );

        final message2 = _createMockMessage(
          messageId: 'firebase_123',
          title: 'Test Title',
          body: 'Test Body',
          data: {'key': 'value'},
        );

        final id1 =
            UniversalNotificationDeduplication.generateMessageId(message1);
        final id2 =
            UniversalNotificationDeduplication.generateMessageId(message2);

        expect(id1, equals(id2));
      });

      test('should generate different IDs for different messages', () {
        final message1 = _createMockMessage(
          messageId: 'firebase_123',
          title: 'Test Title 1',
          body: 'Test Body 1',
        );

        final message2 = _createMockMessage(
          messageId: 'firebase_456',
          title: 'Test Title 2',
          body: 'Test Body 2',
        );

        final id1 =
            UniversalNotificationDeduplication.generateMessageId(message1);
        final id2 =
            UniversalNotificationDeduplication.generateMessageId(message2);

        expect(id1, isNot(equals(id2)));
      });

      test('should handle missing message ID', () {
        final message = _createMockMessage(
          messageId: null,
          title: 'Test Title',
          body: 'Test Body',
          sentTime: DateTime.now(),
        );

        final id =
            UniversalNotificationDeduplication.generateMessageId(message);

        expect(id, isNotNull);
        expect(id, isNotEmpty);
      });
    });
  });
}

// Helper function to create mock RemoteMessage for testing
RemoteMessage _createMockMessage({
  String? messageId,
  String? title,
  String? body,
  Map<String, dynamic>? data,
  DateTime? sentTime,
}) {
  return RemoteMessage(
    messageId: messageId,
    notification: RemoteNotification(
      title: title,
      body: body,
    ),
    data: data ?? {},
    sentTime: sentTime,
  );
}
