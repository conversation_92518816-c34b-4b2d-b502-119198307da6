import 'package:flutter_test/flutter_test.dart';
import 'package:busaty_parents/notification_service/utils/background_notification_deduplication.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Background Notification Deduplication Tests', () {
    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      await CacheHelper.init();
      
      // Clear all background notification cache before each test
      await BackgroundNotificationDeduplication.clearAll();
    });

    group('Basic Deduplication', () {
      test('should allow first notification', () async {
        const messageId = 'bg_test_message_1';
        
        final shouldShow = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        
        expect(shouldShow, isTrue);
      });

      test('should block duplicate notification within window', () async {
        const messageId = 'bg_test_message_2';
        
        // First notification should be allowed
        final firstShow = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        expect(firstShow, isTrue);
        
        // Immediate duplicate should be blocked
        final secondShow = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        expect(secondShow, isFalse);
      });

      test('should handle multiple different notifications', () async {
        const messageId1 = 'bg_test_message_3a';
        const messageId2 = 'bg_test_message_3b';
        const messageId3 = 'bg_test_message_3c';
        
        // All different notifications should be allowed
        final show1 = await BackgroundNotificationDeduplication.shouldShowNotification(messageId1);
        final show2 = await BackgroundNotificationDeduplication.shouldShowNotification(messageId2);
        final show3 = await BackgroundNotificationDeduplication.shouldShowNotification(messageId3);
        
        expect(show1, isTrue);
        expect(show2, isTrue);
        expect(show3, isTrue);
        
        // Duplicates should be blocked
        final duplicate1 = await BackgroundNotificationDeduplication.shouldShowNotification(messageId1);
        final duplicate2 = await BackgroundNotificationDeduplication.shouldShowNotification(messageId2);
        
        expect(duplicate1, isFalse);
        expect(duplicate2, isFalse);
      });
    });

    group('Cache Management', () {
      test('should properly record and check cached messages', () async {
        const messageId = 'bg_cache_test';
        
        // Initially not cached
        final initiallycached = await BackgroundNotificationDeduplication.isMessageCached(messageId);
        expect(initiallycached, isFalse);
        
        // Show notification (should record in cache)
        await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        
        // Should now be cached
        final nowCached = await BackgroundNotificationDeduplication.isMessageCached(messageId);
        expect(nowCached, isTrue);
      });

      test('should provide correct statistics', () async {
        const messageId1 = 'bg_stats_test_1';
        const messageId2 = 'bg_stats_test_2';
        
        // Add some notifications
        await BackgroundNotificationDeduplication.shouldShowNotification(messageId1);
        await BackgroundNotificationDeduplication.shouldShowNotification(messageId2);
        
        final stats = await BackgroundNotificationDeduplication.getStats();
        
        expect(stats['notification_entries'], greaterThanOrEqualTo(2));
        expect(stats['expiry_entries'], greaterThanOrEqualTo(2));
        expect(stats['duplicate_window_minutes'], equals(15));
      });

      test('should cleanup expired entries', () async {
        const messageId = 'bg_cleanup_test';
        
        // Add a notification
        await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        
        // Verify it's cached
        final beforeCleanup = await BackgroundNotificationDeduplication.isMessageCached(messageId);
        expect(beforeCleanup, isTrue);
        
        // Manually set expiry to past time to simulate expiration
        await CacheHelper.putInt('bg_notification_expiry_$messageId', 
            DateTime.now().subtract(Duration(hours: 1)).millisecondsSinceEpoch);
        
        // Run cleanup
        await BackgroundNotificationDeduplication.cleanupExpiredEntries();
        
        // Should no longer be cached
        final afterCleanup = await BackgroundNotificationDeduplication.isMessageCached(messageId);
        expect(afterCleanup, isFalse);
        
        // Should allow notification again after cleanup
        final shouldShow = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        expect(shouldShow, isTrue);
      });

      test('should clear all cache entries', () async {
        const messageId1 = 'bg_clear_test_1';
        const messageId2 = 'bg_clear_test_2';
        
        // Add some notifications
        await BackgroundNotificationDeduplication.shouldShowNotification(messageId1);
        await BackgroundNotificationDeduplication.shouldShowNotification(messageId2);
        
        // Verify they're cached
        final stats1 = await BackgroundNotificationDeduplication.getStats();
        expect(stats1['notification_entries'], greaterThanOrEqualTo(2));
        
        // Clear all
        await BackgroundNotificationDeduplication.clearAll();
        
        // Verify cache is empty
        final stats2 = await BackgroundNotificationDeduplication.getStats();
        expect(stats2['notification_entries'], equals(0));
        expect(stats2['expiry_entries'], equals(0));
      });
    });

    group('Error Handling', () {
      test('should handle cache initialization errors gracefully', () async {
        // This test simulates what happens when cache fails to initialize
        // The method should still return true to avoid missing important notifications
        
        const messageId = 'bg_error_test';
        
        // Even if there are issues, should not throw and should allow notification
        final shouldShow = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        expect(shouldShow, isTrue);
      });

      test('should handle orphaned cache entries', () async {
        const messageId = 'bg_orphan_test';
        
        // Manually create an orphaned entry (notification without expiry)
        await CacheHelper.putBool('bg_notification_$messageId', true);
        // Don't set expiry key
        
        // Should clean up orphaned entry and allow notification
        final shouldShow = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        expect(shouldShow, isTrue);
      });
    });

    group('Background Context Simulation', () {
      test('should work correctly in simulated background context', () async {
        // Simulate multiple background handler invocations
        // Each invocation should maintain deduplication state via persistent storage
        
        const messageId = 'bg_context_test';
        
        // First background handler invocation
        final firstInvocation = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        expect(firstInvocation, isTrue);
        
        // Simulate app being killed and restarted (clear any in-memory state)
        // In real background context, static variables would be reset
        
        // Second background handler invocation with same message
        final secondInvocation = await BackgroundNotificationDeduplication.shouldShowNotification(messageId);
        expect(secondInvocation, isFalse); // Should be blocked due to persistent cache
        
        // Third invocation with different message
        const differentMessageId = 'bg_context_test_different';
        final thirdInvocation = await BackgroundNotificationDeduplication.shouldShowNotification(differentMessageId);
        expect(thirdInvocation, isTrue); // Should be allowed
      });
    });
  });
}
