package com.busaty.parent

import android.util.Log
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService

class CustomFirebaseMessagingService : FirebaseMessagingService() {
    
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d("CustomFCMService", "Message received: ${remoteMessage.messageId}")
        
        // CRITICAL: Do NOT call super.onMessageReceived()
        // This prevents Firebase from showing automatic notifications
        
        // Instead, delegate to Flutter's background service
        try {
            val intent = android.content.Intent(this, FlutterFirebaseMessagingBackgroundService::class.java)
            intent.putExtra("message", remoteMessage)
            startService(intent)
            Log.d("CustomFCMService", "Delegated to Flutter background service")
        } catch (e: Exception) {
            Log.e("CustomFCMService", "Error delegating to Flutter service: ${e.message}")
            // Fallback: let Flutter handle it normally
            super.onMessageReceived(remoteMessage)
        }
    }
    
    override fun onNewToken(token: String) {
        Log.d("CustomFCMService", "New FCM token: $token")
        super.onNewToken(token)
    }
}
