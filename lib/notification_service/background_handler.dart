import 'package:firebase_messaging/firebase_messaging.dart';
import 'handlers/message_handler.dart';
import 'utils/logger.dart';

/// Background message handler for Firebase Cloud Messaging
/// This function runs in an isolate and handles messages when the app is in background or terminated

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    NotificationLogger.info(
        'Background handler started: ${message.messageId} - ${message.notification?.title}');

    // Initialize the message handler (this will also initialize cache)
    await MessageHandler.initialize();

    // Handle the message using the universal handler (background mode)
    await MessageHandler.handleMessage(message, isForeground: false);

    NotificationLogger.info('Background message handled successfully');
  } catch (e) {
    NotificationLogger.error('Error in background message handler: $e');

    // In case of error, we might want to show a basic notification
    // to ensure important messages aren't lost
    try {
      // This is a fallback - create a simple notification without deduplication
      // Only use this as last resort to prevent missing critical notifications
      NotificationLogger.warning(
          'Attempting fallback notification due to error');

      // You could implement a basic fallback notification here if needed
      // For now, we'll just log the error and continue
    } catch (fallbackError) {
      NotificationLogger.error(
          'Fallback notification also failed: $fallbackError');
    }
  }
}
