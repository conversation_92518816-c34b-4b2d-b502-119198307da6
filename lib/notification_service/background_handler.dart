import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/notification_service/utils/background_notification_deduplication.dart';
import 'package:busaty_parents/notification_service/utils/unified_notification_deduplication.dart';
import 'package:busaty_parents/helper/cache_helper.dart';

/// Helper function to generate consistent message IDs
String _generateConsistentMessageId(RemoteMessage message) {
  if (message.messageId != null && message.messageId!.isNotEmpty) {
    return message.messageId!;
  }

  // If no message ID, create a deterministic ID based on message content
  final String contentHash = '${message.notification?.title ?? ''}'
      '${message.notification?.body ?? ''}'
      '${message.data.toString()}'
      '${message.sentTime?.millisecondsSinceEpoch ?? 0}';

  return contentHash.hashCode.toString();
}

/// Global background message handler for Firebase Messaging
/// This should be registered only once at the top level to avoid duplicates
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    // Use a consistent ID based on the message ID to prevent duplicates
    final String messageId = _generateConsistentMessageId(message);

    Logger.firebase(
        'Background handler started: $messageId - ${message.notification?.title}');

    // Use background-specific deduplication system for better reliability
    // in background/terminated contexts
    final shouldShow =
        await BackgroundNotificationDeduplication.shouldShowNotification(
            messageId);

    if (!shouldShow) {
      Logger.w('Skipping duplicate background notification: $messageId');
      return;
    }

    final notification = message.notification;
    if (notification != null) {
      // Use the message ID as the notification ID to prevent duplicates
      final notificationId = messageId.hashCode;

      // Create a payload with the message data
      final Map<String, dynamic> data = Map<String, dynamic>.from(message.data);
      data['_messageId'] = messageId;

      Logger.firebase(
          'Showing background notification with ID: $notificationId');

      // Show notification using local notifications
      final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

      // Initialize the plugin for background use
      const androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings();
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await flutterLocalNotificationsPlugin.initialize(initSettings);

      await flutterLocalNotificationsPlugin.show(
        notificationId,
        notification.title,
        notification.body,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'default_channel',
            'Default Channel',
            channelDescription: 'Default notification channel',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: data.toString(),
      );

      Logger.firebase('Background notification displayed successfully');
    }
  } catch (error, stackTrace) {
    Logger.e('Error in background message handler', error, stackTrace);
  }
}

/// Clean up expired notification cache entries with batching and rate limiting
Future<void> cleanupExpiredNotifications() async {
  try {
    await CacheHelper.init();

    final now = DateTime.now().millisecondsSinceEpoch;
    final keys = CacheHelper.getKeys();

    // Check if cleanup was done recently (within last hour)
    final lastCleanupKey = 'last_notification_cleanup';
    final lastCleanup = CacheHelper.getInt(lastCleanupKey) ?? 0;
    final timeSinceLastCleanup = now - lastCleanup;

    if (timeSinceLastCleanup < 3600000) {
      // 1 hour in milliseconds
      Logger.i('Notification cleanup skipped - done recently');
      return;
    }

    // Clean up both old and new deduplication systems
    int cleanedCount = 0;
    final expiredKeys = <String>[];

    // Clean up old system entries
    for (final key in keys) {
      if (key.startsWith('notification_expiry_')) {
        final expiryTime = CacheHelper.getInt(key);
        if (expiryTime != null && expiryTime < now) {
          final messageId = key.replaceFirst('notification_expiry_', '');
          expiredKeys.add('notification_$messageId');
          expiredKeys.add(key);
          cleanedCount++;
        }
      }
    }

    // Batch remove expired entries
    for (final key in expiredKeys) {
      await CacheHelper.remove(key);
    }

    // Clean up unified deduplication system
    await UnifiedNotificationDeduplication.cleanupExpiredEntries();

    // Clean up background deduplication system
    await BackgroundNotificationDeduplication.cleanupExpiredEntries();

    // Update last cleanup time
    await CacheHelper.putInt(lastCleanupKey, now);

    Logger.i(
        'Cleaned up $cleanedCount expired notification entries (legacy system)');
  } catch (e) {
    Logger.e('Error cleaning up expired notifications: $e');
  }
}
