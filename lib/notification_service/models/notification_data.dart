import 'package:firebase_messaging/firebase_messaging.dart';

/// Represents notification data with all necessary information
class NotificationData {
  final String id;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  final DateTime receivedAt;
  final String? firebaseMessageId;
  final String? imageUrl;
  final String? sound;

  const NotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.data,
    required this.receivedAt,
    this.firebaseMessageId,
    this.imageUrl,
    this.sound,
  });

  /// Create NotificationData from Firebase RemoteMessage
  factory NotificationData.fromRemoteMessage(RemoteMessage message) {
    final notification = message.notification;

    return NotificationData(
      id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: notification?.title ?? '',
      body: notification?.body ?? '',
      data: Map<String, dynamic>.from(message.data),
      receivedAt: DateTime.now(),
      firebaseMessageId: message.messageId,
      imageUrl:
          notification?.android?.imageUrl ?? notification?.apple?.imageUrl,
      sound: notification?.android?.sound ??
          notification?.apple?.sound?.toString(),
    );
  }

  /// Create a copy with updated ID
  NotificationData copyWith({
    String? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    DateTime? receivedAt,
    String? firebaseMessageId,
    String? imageUrl,
    String? sound,
  }) {
    return NotificationData(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      receivedAt: receivedAt ?? this.receivedAt,
      firebaseMessageId: firebaseMessageId ?? this.firebaseMessageId,
      imageUrl: imageUrl ?? this.imageUrl,
      sound: sound ?? this.sound,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'data': data,
      'receivedAt': receivedAt.millisecondsSinceEpoch,
      'firebaseMessageId': firebaseMessageId,
      'imageUrl': imageUrl,
      'sound': sound,
    };
  }

  /// Create from JSON
  factory NotificationData.fromJson(Map<String, dynamic> json) {
    return NotificationData(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      receivedAt: DateTime.fromMillisecondsSinceEpoch(json['receivedAt'] ?? 0),
      firebaseMessageId: json['firebaseMessageId'],
      imageUrl: json['imageUrl'],
      sound: json['sound'],
    );
  }

  /// Check if notification has valid content
  bool get hasValidContent {
    return title.isNotEmpty || body.isNotEmpty;
  }

  /// Get display title (fallback to app name if empty)
  String get displayTitle {
    return title.isNotEmpty ? title : 'Notification';
  }

  /// Get display body (fallback to default message if empty)
  String get displayBody {
    return body.isNotEmpty ? body : 'You have a new notification';
  }

  @override
  String toString() {
    return 'NotificationData(id: $id, title: "$title", body: "$body", firebaseId: $firebaseMessageId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationData &&
        other.id == id &&
        other.title == title &&
        other.body == body &&
        other.firebaseMessageId == firebaseMessageId;
  }

  @override
  int get hashCode {
    return Object.hash(id, title, body, firebaseMessageId);
  }
}
