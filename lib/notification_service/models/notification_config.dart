/// Configuration for the notification system
class NotificationConfig {
  /// How long to prevent duplicate notifications (main window)
  static const Duration deduplicationWindow = Duration(minutes: 20);
  
  /// Rapid-fire duplicate prevention window
  static const Duration rapidFireWindow = Duration(seconds: 30);
  
  /// Maximum number of cached notification entries
  static const int maxCacheEntries = 1000;
  
  /// How often to clean up expired cache entries
  static const Duration cleanupInterval = Duration(hours: 1);
  
  /// FCM token retry configuration
  static const int maxTokenRetries = 8;
  static const Duration initialTokenRetryDelay = Duration(milliseconds: 100);
  static const Duration maxTokenRetryDelay = Duration(seconds: 30);
  
  /// Cache key prefixes
  static const String notificationCachePrefix = 'notif_';
  static const String expiryPrefix = 'notif_exp_';
  static const String lastShownPrefix = 'notif_last_';
  static const String cleanupPrefix = 'notif_cleanup_';
  
  /// Notification channel configuration
  static const String defaultChannelId = 'default_notification_channel';
  static const String defaultChannelName = 'Default Notifications';
  static const String defaultChannelDescription = 'Default notification channel';
  
  /// High priority channel for important notifications
  static const String highPriorityChannelId = 'high_priority_channel';
  static const String highPriorityChannelName = 'Important Notifications';
  static const String highPriorityChannelDescription = 'High priority notifications';
  
  /// Sound and vibration settings
  static const String defaultSound = 'default';
  static const bool enableVibration = true;
  static const bool enableLights = true;
  
  /// Debug and logging configuration
  static const bool enableDebugLogging = true;
  static const bool enablePerformanceLogging = false;
  
  /// Platform-specific settings
  static const bool showNotificationInForeground = true;
  static const bool autoInitializeOnAppStart = true;
  
  /// Error handling configuration
  static const bool failSafeMode = true; // Allow notifications on errors to prevent missing important ones
  static const int maxErrorRetries = 3;
  
  /// Performance optimization settings
  static const bool enableBatchOperations = true;
  static const int batchSize = 50;
  
  /// Get cache expiry time for a notification
  static DateTime getCacheExpiryTime([DateTime? baseTime]) {
    return (baseTime ?? DateTime.now()).add(deduplicationWindow);
  }
  
  /// Get rapid-fire expiry time
  static DateTime getRapidFireExpiryTime([DateTime? baseTime]) {
    return (baseTime ?? DateTime.now()).add(rapidFireWindow);
  }
  
  /// Check if debug logging is enabled
  static bool get isDebugMode {
    return enableDebugLogging;
  }
  
  /// Get retry delay with exponential backoff
  static Duration getRetryDelay(int attempt) {
    final delay = initialTokenRetryDelay * (1 << attempt);
    return delay > maxTokenRetryDelay ? maxTokenRetryDelay : delay;
  }
  
  /// Validate configuration
  static bool validateConfig() {
    if (deduplicationWindow.inSeconds < 60) {
      throw ArgumentError('Deduplication window must be at least 1 minute');
    }
    
    if (rapidFireWindow.inSeconds < 5) {
      throw ArgumentError('Rapid fire window must be at least 5 seconds');
    }
    
    if (maxCacheEntries < 100) {
      throw ArgumentError('Max cache entries must be at least 100');
    }
    
    if (maxTokenRetries < 1) {
      throw ArgumentError('Max token retries must be at least 1');
    }
    
    return true;
  }
}
