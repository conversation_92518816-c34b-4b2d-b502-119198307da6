/// Configuration for the notification system
class NotificationConfig {
  /// FCM token retry configuration
  static const int maxTokenRetries = 8;
  static const Duration initialTokenRetryDelay = Duration(milliseconds: 100);
  static const Duration maxTokenRetryDelay = Duration(seconds: 30);

  /// Notification channel configuration
  static const String defaultChannelId = 'default_notification_channel';
  static const String defaultChannelName = 'Default Notifications';
  static const String defaultChannelDescription =
      'Default notification channel';

  /// High priority channel for important notifications
  static const String highPriorityChannelId = 'high_priority_channel';
  static const String highPriorityChannelName = 'Important Notifications';
  static const String highPriorityChannelDescription =
      'High priority notifications';

  /// Sound and vibration settings
  static const String defaultSound = 'default';
  static const bool enableVibration = true;
  static const bool enableLights = true;

  /// Debug and logging configuration
  static const bool enableDebugLogging = true;
  static const bool enablePerformanceLogging = false;

  /// Platform-specific settings
  static const bool showNotificationInForeground = true;
  static const bool autoInitializeOnAppStart = true;

  /// Error handling configuration
  static const bool failSafeMode =
      true; // Allow notifications on errors to prevent missing important ones
  static const int maxErrorRetries = 3;

  /// Performance optimization settings
  static const bool enableBatchOperations = true;
  static const int batchSize = 50;

  /// Check if debug logging is enabled
  static bool get isDebugMode {
    return enableDebugLogging;
  }

  /// Get retry delay with exponential backoff
  static Duration getRetryDelay(int attempt) {
    final delay = initialTokenRetryDelay * (1 << attempt);
    return delay > maxTokenRetryDelay ? maxTokenRetryDelay : delay;
  }

  /// Validate configuration
  static bool validateConfig() {
    if (maxTokenRetries < 1) {
      throw ArgumentError('Max token retries must be at least 1');
    }

    return true;
  }
}
