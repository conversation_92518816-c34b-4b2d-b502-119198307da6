import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'models/notification_config.dart';
import 'handlers/message_handler.dart';
import 'storage/notification_cache.dart';
import 'utils/logger.dart';

/// Main notification manager - single entry point for all notification functionality
class NotificationManager {
  static NotificationManager? _instance;
  static bool _isInitialized = false;

  // Private constructor
  NotificationManager._();

  /// Get singleton instance
  static NotificationManager get instance {
    _instance ??= NotificationManager._();
    return _instance!;
  }

  /// Initialize the complete notification system
  static Future<void> initialize() async {
    if (_isInitialized) {
      NotificationLogger.debug('Notification system already initialized');
      return;
    }

    try {
      NotificationLogger.info('Initializing notification system...');

      // Validate configuration
      NotificationConfig.validateConfig();

      // Initialize message handler
      await MessageHandler.initialize();

      // Set up Firebase message handlers
      await _setupFirebaseHandlers();

      // Initialize FCM token management
      await _initializeFCMToken();

      // Schedule cleanup
      await NotificationCache.cleanupExpiredEntries();

      _isInitialized = true;
      NotificationLogger.info('Notification system initialized successfully');
    } catch (e) {
      NotificationLogger.error('Failed to initialize notification system: $e');
      rethrow;
    }
  }

  /// Set up Firebase message handlers for all app states
  static Future<void> _setupFirebaseHandlers() async {
    try {
      // IMPORTANT: Only set up foreground handler
      // Background handler is set up separately in main.dart to avoid duplicates

      // Handle messages when app is in foreground
      FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
        NotificationLogger.info(
            'Foreground message received: ${message.messageId}');
        await MessageHandler.handleMessage(message);
      });

      // Handle notification taps when app is in background
      FirebaseMessaging.onMessageOpenedApp
          .listen((RemoteMessage message) async {
        NotificationLogger.info(
            'Background notification tapped: ${message.messageId}');
        // Handle navigation or app state updates here
      });

      // Check for initial message (app opened from terminated state)
      final initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        NotificationLogger.info(
            'App opened from terminated state with message: ${initialMessage.messageId}');
        // Handle initial message here
      }

      NotificationLogger.debug('Firebase message handlers set up');
    } catch (e) {
      NotificationLogger.error('Error setting up Firebase handlers: $e');
      rethrow;
    }
  }

  /// Initialize FCM token with retry mechanism
  static Future<void> _initializeFCMToken() async {
    int retries = 0;

    while (retries < NotificationConfig.maxTokenRetries) {
      try {
        // Request permission first
        final settings = await FirebaseMessaging.instance.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
        );

        if (settings.authorizationStatus == AuthorizationStatus.denied) {
          NotificationLogger.warning('Notification permission denied by user');
          return;
        }

        // Get FCM token
        final token = await FirebaseMessaging.instance.getToken();

        if (token != null && token.isNotEmpty) {
          // Cache the token
          await CacheHelper.putString('fcmToken', token);

          NotificationLogger.info('FCM token obtained and cached successfully');
          NotificationLogger.debug('FCM token: ${token.substring(0, 20)}...');

          // Listen for token refresh
          FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
            await CacheHelper.putString('fcmToken', newToken);
            NotificationLogger.info('FCM token refreshed and cached');
          });

          return;
        }

        throw Exception('FCM token is null or empty');
      } catch (e) {
        retries++;
        NotificationLogger.warning(
            'FCM token attempt $retries/${NotificationConfig.maxTokenRetries} failed: $e');

        if (retries < NotificationConfig.maxTokenRetries) {
          final delay = NotificationConfig.getRetryDelay(retries);
          await Future.delayed(delay);
        } else {
          NotificationLogger.error(
              'Failed to get FCM token after ${NotificationConfig.maxTokenRetries} attempts');
          // Don't throw - app should continue without FCM token
        }
      }
    }
  }

  /// Get current FCM token
  static Future<String?> getFCMToken() async {
    try {
      // Try cached token first
      final cachedToken = CacheHelper.getString('fcmToken');
      if (cachedToken != null && cachedToken.isNotEmpty) {
        return cachedToken;
      }

      // Get fresh token
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        await CacheHelper.putString('fcmToken', token);
      }

      return token;
    } catch (e) {
      NotificationLogger.error('Error getting FCM token: $e');
      return null;
    }
  }

  /// Check if notification system is initialized
  static bool get isInitialized => _isInitialized;

  /// Get comprehensive system status
  static Future<Map<String, dynamic>> getSystemStatus() async {
    try {
      final cacheStats = await NotificationCache.getStats();
      final handlerStatus = MessageHandler.getStatus();
      final fcmToken = await getFCMToken();

      return {
        'system_initialized': _isInitialized,
        'fcm_token_available': fcmToken != null,
        'fcm_token_length': fcmToken?.length ?? 0,
        'cache_stats': cacheStats,
        'handler_status': handlerStatus,
        'config': {
          'deduplication_window_minutes':
              NotificationConfig.deduplicationWindow.inMinutes,
          'rapid_fire_window_seconds':
              NotificationConfig.rapidFireWindow.inSeconds,
          'max_cache_entries': NotificationConfig.maxCacheEntries,
          'fail_safe_mode': NotificationConfig.failSafeMode,
        },
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      NotificationLogger.error('Error getting system status: $e');
      return {
        'error': e.toString(),
        'system_initialized': _isInitialized,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Clear all notification data (for testing/debugging)
  static Future<void> clearAllData() async {
    try {
      await NotificationCache.clearAll();
      NotificationLogger.info('All notification data cleared');
    } catch (e) {
      NotificationLogger.error('Error clearing notification data: $e');
    }
  }

  /// Test notification functionality
  static Future<bool> testNotificationSystem() async {
    try {
      NotificationLogger.info('Testing notification system...');

      // Test cache functionality
      await NotificationCache.initialize();
      final testId = 'test_${DateTime.now().millisecondsSinceEpoch}';

      // Should allow first notification
      final shouldShow1 =
          await NotificationCache.shouldShowNotification(testId);
      if (!shouldShow1) {
        throw Exception('First test notification was blocked');
      }

      // Should block duplicate
      final shouldShow2 =
          await NotificationCache.shouldShowNotification(testId);
      if (shouldShow2) {
        throw Exception('Duplicate test notification was not blocked');
      }

      // Clean up test data
      await NotificationCache.clearAll();

      NotificationLogger.info('Notification system test passed');
      return true;
    } catch (e) {
      NotificationLogger.error('Notification system test failed: $e');
      return false;
    }
  }

  /// Force refresh FCM token
  static Future<String?> refreshFCMToken() async {
    try {
      await FirebaseMessaging.instance.deleteToken();
      final newToken = await FirebaseMessaging.instance.getToken();

      if (newToken != null) {
        await CacheHelper.putString('fcmToken', newToken);
        NotificationLogger.info('FCM token refreshed successfully');
      }

      return newToken;
    } catch (e) {
      NotificationLogger.error('Error refreshing FCM token: $e');
      return null;
    }
  }
}
