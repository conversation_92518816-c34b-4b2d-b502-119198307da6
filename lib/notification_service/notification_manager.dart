import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'models/notification_config.dart';
import 'handlers/message_handler.dart';
import 'utils/logger.dart';

/// Main notification manager - single entry point for all notification functionality
class NotificationManager {
  static NotificationManager? _instance;
  static bool _isInitialized = false;

  // Private constructor
  NotificationManager._();

  /// Get singleton instance
  static NotificationManager get instance {
    _instance ??= NotificationManager._();
    return _instance!;
  }

  /// Initialize the complete notification system
  static Future<void> initialize() async {
    if (_isInitialized) {
      NotificationLogger.debug('Notification system already initialized');
      return;
    }

    try {
      NotificationLogger.info('Initializing notification system...');

      // Validate configuration
      NotificationConfig.validateConfig();

      // Initialize message handler
      await MessageHandler.initialize();

      // Set up ONLY foreground message handler (background is in main.dart)
      await _setupForegroundHandler();

      // Initialize FCM token management
      await _initializeFCMToken();

      _isInitialized = true;
      NotificationLogger.info('Notification system initialized successfully');
    } catch (e) {
      NotificationLogger.error('Failed to initialize notification system: $e');
      rethrow;
    }
  }

  /// Set up ONLY foreground message handler (background handler is in main.dart)
  static Future<void> _setupForegroundHandler() async {
    try {
      NotificationLogger.info('Setting up foreground message handler...');

      // Handle messages when app is in foreground - SINGLE HANDLER
      FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        NotificationLogger.info(
            '🌟 FOREGROUND HANDLER TRIGGERED at $timestamp: ${message.messageId} - ${message.notification?.title}');
        NotificationLogger.debug(
            '📊 FOREGROUND STATS: Called at $timestamp for Firebase ID ${message.messageId}');

        await MessageHandler.handleMessage(message, isForeground: true);

        NotificationLogger.debug(
            'FOREGROUND HANDLER: Message processing completed');
      });

      // Handle notification taps when app is in background
      FirebaseMessaging.onMessageOpenedApp
          .listen((RemoteMessage message) async {
        NotificationLogger.info(
            'Background notification tapped: ${message.messageId}');
        // Handle navigation or app state updates here
      });

      // Check for initial message (app opened from terminated state)
      final initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        NotificationLogger.info(
            'App opened from terminated state with message: ${initialMessage.messageId}');
        // Handle initial message here
      }

      NotificationLogger.info('Foreground message handler set up successfully');
    } catch (e) {
      NotificationLogger.error('Error setting up foreground handler: $e');
      rethrow;
    }
  }

  /// Initialize FCM token with retry mechanism
  static Future<void> _initializeFCMToken() async {
    int retries = 0;

    while (retries < NotificationConfig.maxTokenRetries) {
      try {
        // Request permission first
        final settings = await FirebaseMessaging.instance.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
        );

        if (settings.authorizationStatus == AuthorizationStatus.denied) {
          NotificationLogger.warning('Notification permission denied by user');
          return;
        }

        // Get FCM token
        final token = await FirebaseMessaging.instance.getToken();

        if (token != null && token.isNotEmpty) {
          // Cache the token
          await CacheHelper.putString('fcmToken', token);

          NotificationLogger.info('FCM token obtained and cached successfully');
          NotificationLogger.debug('FCM token: ${token.substring(0, 20)}...');

          // Listen for token refresh
          FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
            await CacheHelper.putString('fcmToken', newToken);
            NotificationLogger.info('FCM token refreshed and cached');
          });

          return;
        }

        throw Exception('FCM token is null or empty');
      } catch (e) {
        retries++;
        NotificationLogger.warning(
            'FCM token attempt $retries/${NotificationConfig.maxTokenRetries} failed: $e');

        if (retries < NotificationConfig.maxTokenRetries) {
          final delay = NotificationConfig.getRetryDelay(retries);
          await Future.delayed(delay);
        } else {
          NotificationLogger.error(
              'Failed to get FCM token after ${NotificationConfig.maxTokenRetries} attempts');
          // Don't throw - app should continue without FCM token
        }
      }
    }
  }

  /// Get current FCM token
  static Future<String?> getFCMToken() async {
    try {
      // Try cached token first
      final cachedToken = CacheHelper.getString('fcmToken');
      if (cachedToken != null && cachedToken.isNotEmpty) {
        return cachedToken;
      }

      // Get fresh token
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        await CacheHelper.putString('fcmToken', token);
      }

      return token;
    } catch (e) {
      NotificationLogger.error('Error getting FCM token: $e');
      return null;
    }
  }

  /// Check if notification system is initialized
  static bool get isInitialized => _isInitialized;

  /// Get comprehensive system status
  static Future<Map<String, dynamic>> getSystemStatus() async {
    try {
      final handlerStatus = MessageHandler.getStatus();
      final fcmToken = await getFCMToken();

      return {
        'system_initialized': _isInitialized,
        'fcm_token_available': fcmToken != null,
        'fcm_token_length': fcmToken?.length ?? 0,
        'handler_status': handlerStatus,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      NotificationLogger.error('Error getting system status: $e');
      return {
        'error': e.toString(),
        'system_initialized': _isInitialized,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Test notification functionality
  static Future<bool> testNotificationSystem() async {
    try {
      NotificationLogger.info('Testing notification system...');

      // Test basic initialization
      if (!_isInitialized) {
        await initialize();
      }

      // Test FCM token availability
      final token = await getFCMToken();
      if (token == null) {
        throw Exception('FCM token not available');
      }

      NotificationLogger.info('Notification system test passed');
      return true;
    } catch (e) {
      NotificationLogger.error('Notification system test failed: $e');
      return false;
    }
  }

  /// Force refresh FCM token
  static Future<String?> refreshFCMToken() async {
    try {
      await FirebaseMessaging.instance.deleteToken();
      final newToken = await FirebaseMessaging.instance.getToken();

      if (newToken != null) {
        await CacheHelper.putString('fcmToken', newToken);
        NotificationLogger.info('FCM token refreshed successfully');
      }

      return newToken;
    } catch (e) {
      NotificationLogger.error('Error refreshing FCM token: $e');
      return null;
    }
  }
}
