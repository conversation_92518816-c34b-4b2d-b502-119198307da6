import 'dart:async';
import 'dart:io' show Platform;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/notification_service/errors/notification_exception.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/notification_service/utils/unified_notification_deduplication.dart';
import 'package:timezone/timezone.dart' as tz;
import 'errors/error_handler.dart';
import 'notification_payload.dart';
import 'notification_service_interface.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class LocalNotificationService implements INotificationService {
  final FlutterLocalNotificationsPlugin _notifications;
  final _notificationController =
      StreamController<NotificationPayload>.broadcast();
  final _notificationTapController =
      StreamController<NotificationPayload>.broadcast();

  LocalNotificationService(this._notifications);

  static Future<String?> get firebaseToken async {
    try {
      final messaging = FirebaseMessaging.instance;

      if (Platform.isIOS) {
        Logger.i('iOS: Starting APNS registration process...');

        // Enable auto-init and configure foreground notifications
        await messaging.setAutoInitEnabled(true);
        await messaging.setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
          sound: true,
        );

        // Wait longer initially for APNS setup
        await Future.delayed(const Duration(seconds: 0));

        // Try to get APNS token with longer timeout
        String? apnsToken;
        final completer = Completer<String?>();

        // Start a timeout timer
        Timer(const Duration(seconds: 30), () {
          if (!completer.isCompleted) {
            completer.complete(null);
          }
        });

        // Check for token periodically
        Timer.periodic(const Duration(seconds: 2), (timer) async {
          apnsToken = await messaging.getAPNSToken();
          Logger.i(
              'iOS: Checking APNS token: ${apnsToken != null ? "received" : "waiting"}');

          if (apnsToken != null && !completer.isCompleted) {
            completer.complete(apnsToken);
            timer.cancel();
          }
        });

        // Wait for either token or timeout
        apnsToken = await completer.future;

        if (apnsToken == null) {
          throw NotificationException('Failed to get APNS token after timeout',
              code: 'apns-token-timeout');
        }

        Logger.i('iOS: Successfully received APNS token');
      }

      // Check cached token first
      String? cachedToken = CacheHelper.getString("fcmToken");
      if (cachedToken != null && cachedToken.isNotEmpty) {
        Logger.i('Using cached FCM token');
        return cachedToken;
      }

      // Get FCM token with exponential backoff retry
      Logger.i('Requesting new FCM token...');
      String? token;
      int retries = 0;
      const maxRetries = 8; // Increased retries

      while (token == null && retries < maxRetries) {
        try {
          // Add longer delay for token generation
          if (retries > 0) {
            final delay = Duration(
                seconds: (2 << retries)
                    .clamp(2, 30)); // Exponential backoff, max 30s
            Logger.i(
                'Retrying FCM token request in ${delay.inSeconds}s (attempt ${retries + 1}/$maxRetries)');
            await Future.delayed(delay);
          }

          token = await messaging.getToken();

          if (token != null) {
            Logger.i(
                'FCM token successfully received: ${token.substring(0, 20)}...');
            await CacheHelper.putString("fcmToken", token);

            // Verify token was cached properly
            final verifyToken = CacheHelper.getString("fcmToken");
            if (verifyToken != token) {
              Logger.w('Token caching verification failed, retrying...');
              await CacheHelper.putString("fcmToken", token);
            }

            return token;
          }
        } catch (tokenError) {
          Logger.w(
              'FCM token request failed (attempt ${retries + 1}): $tokenError');
        }

        retries++;
      }

      throw NotificationException(
          'Failed to get FCM token after $maxRetries attempts',
          code: 'fcm-token-failed');
    } catch (e) {
      Logger.e('Error in token registration process: $e');
      return null;
    }
  }

  @override
  Future<void> initialize() async {
    try {
      Logger.i('Initializing notification service');

      // Basic setup first
      await _setupNotificationChannels();

      const androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // Initialize local notifications
      final initialized = await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (!initialized!) {
        throw NotificationException(
          'Failed to initialize local notifications',
          code: 'init_failed',
        );
      }

      // Add a small delay before requesting Firebase permissions
      await Future.delayed(const Duration(milliseconds: 500));

      // Request Firebase permissions
      try {
        final settings = await FirebaseMessaging.instance.requestPermission(
          alert: true,
          badge: true,
          sound: true,
        );

        if (settings.authorizationStatus != AuthorizationStatus.authorized) {
          Logger.w(
              'Notification permissions not granted: ${settings.authorizationStatus}');
        }
      } catch (e) {
        Logger.w('Failed to request Firebase permissions: $e');
        // Continue initialization even if Firebase permissions fail
      }

      // Configure Firebase messaging
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      // Set up message handlers
      // DO NOT register background handler here to avoid duplicates
      // Background handler should be registered only once at the top level
      _setupMessageHandlers();

      Logger.i('Notification service initialized successfully');
    } catch (error, stackTrace) {
      Logger.e('Notification initialization failed', error, stackTrace);
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  // Flag to track if we've already set up message handlers
  static bool _messageHandlersInitialized = false;

  // Public method to check if message handlers are initialized
  static bool get isInitialized => _messageHandlersInitialized;

  // Helper method to generate consistent message IDs
  String _generateConsistentMessageId(RemoteMessage message) {
    if (message.messageId != null && message.messageId!.isNotEmpty) {
      return message.messageId!;
    }

    // If no message ID, create a deterministic ID based on message content
    final String contentHash = '${message.notification?.title ?? ''}'
        '${message.notification?.body ?? ''}'
        '${message.data.toString()}'
        '${message.sentTime?.millisecondsSinceEpoch ?? 0}';

    return contentHash.hashCode.toString();
  }

  void _setupMessageHandlers() {
    // Only set up the handlers once to prevent duplicate notifications
    if (_messageHandlersInitialized) {
      Logger.i('Message handlers already initialized, skipping');
      return;
    }

    Logger.i('Setting up message handlers for the first time');
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      // Use a consistent ID based on the message ID to prevent duplicates
      final String messageId = _generateConsistentMessageId(message);
      Logger.firebase(
          'Received foreground message: $messageId - ${message.notification?.title}');

      // Check for duplicate using unified deduplication system
      final shouldShow =
          await UnifiedNotificationDeduplication.shouldShowNotification(
              messageId);

      if (!shouldShow) {
        Logger.w('Skipping duplicate foreground notification: $messageId');
        return;
      }

      final notification = message.notification;
      if (notification != null) {
        // Create a payload with the message data and include the message ID
        final Map<String, dynamic> data =
            Map<String, dynamic>.from(message.data);
        // Add the message ID to the data so we can use it for notification ID
        data['_messageId'] = messageId;

        final payload = NotificationPayload(
          title: notification.title ?? '',
          body: notification.body ?? '',
          data: data,
        );

        // Show the notification with the message ID as the notification ID
        _showNotificationWithId(
            notificationId: messageId.hashCode, payload: payload);
      }
    });

    // Mark as initialized
    _messageHandlersInitialized = true;
  }

  void _onNotificationTapped(NotificationResponse response) {
    try {
      if (response.payload != null) {
        final notification = NotificationPayload(
          title: response.notificationResponseType ==
                  NotificationResponseType.selectedNotification
              ? 'Notification Tapped'
              : 'Notification Action',
          body: response.actionId ?? 'Notification tapped',
          data: {'payload': response.payload},
        );
        _notificationTapController.add(notification);
      }
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
    }
  }

  @override
  Future<void> showNotification(NotificationPayload payload) async {
    try {
      // Check if this notification has a message ID in its data
      String? messageId;
      if (payload.data != null && payload.data!.containsKey('_messageId')) {
        messageId = payload.data!['_messageId'] as String?;
      }

      // Generate a notification ID
      int notificationId;
      if (messageId != null) {
        // If we have a message ID, use it for consistent notification ID
        notificationId = messageId.hashCode;
        Logger.firebase(
            'Using message ID for notification: $messageId -> $notificationId');
      } else {
        // Otherwise, generate an ID based on the content
        final String idBase =
            '${payload.title}:${payload.body}:${payload.data?.hashCode ?? 0}';
        notificationId = idBase.hashCode;
        Logger.firebase(
            'Generated content-based notification ID: $notificationId');
      }

      // Show the notification with the determined ID
      await _showNotificationWithId(
        notificationId: notificationId,
        payload: payload,
      );
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Future<void> showScheduledNotification({
    required NotificationPayload payload,
    required DateTime scheduledDate,
  }) async {
    try {
      if (scheduledDate.isBefore(DateTime.now())) {
        throw NotificationException(
          'Scheduled date must be in the future',
          code: 'invalid_schedule_time',
        );
      }

      const androidDetails = AndroidNotificationDetails(
        'scheduled_channel',
        'Scheduled Channel',
        channelDescription: 'Channel for scheduled notifications',
        importance: Importance.high,
        priority: Priority.high,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final id = DateTime.now().millisecondsSinceEpoch.remainder(100000);
      await _notifications.zonedSchedule(
        id,
        payload.title,
        payload.body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        details,
        androidScheduleMode: AndroidScheduleMode.alarmClock,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload.data?.toString(),
      );
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Future<void> cancelNotification(int id) async {
    try {
      await _notifications.cancel(id);
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Stream<NotificationPayload> get onNotificationReceived =>
      _notificationController.stream;

  @override
  Stream<NotificationPayload> get onNotificationTapped =>
      _notificationTapController.stream;

  void dispose() {
    _notificationController.close();
    _notificationTapController.close();
  }

  Future<void> _setupNotificationChannels() async {
    const androidChannel = AndroidNotificationChannel(
      'default_channel', // same as in AndroidManifest.xml
      'Default Channel',
      description: 'Default notification channel',
      importance: Importance.high,
      enableVibration: true,
      enableLights: true,
      showBadge: true,
    );

    await _notifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  // Helper method to show a notification with a specific ID
  Future<void> _showNotificationWithId({
    required int notificationId,
    required NotificationPayload payload,
  }) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'default_channel',
        'Default Channel',
        channelDescription: 'Default notification channel',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      Logger.firebase(
          'Showing notification with ID: $notificationId for title: ${payload.title}');

      await _notifications.show(
        notificationId,
        payload.title,
        payload.body,
        details,
        payload: payload.data?.toString(),
      );

      _notificationController.add(payload);
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }
}

// Background message handler is now in background_handler.dart
// and registered only once in main.dart to prevent duplicates
