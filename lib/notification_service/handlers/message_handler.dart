import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../models/notification_data.dart';
import '../models/notification_config.dart';
import '../utils/logger.dart';

/// Universal message handler for all app states (foreground, background, terminated)
class MessageHandler {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  /// Initialize the message handler
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize local notifications
      await _initializeLocalNotifications();

      _isInitialized = true;
      NotificationLogger.info('Message handler initialized successfully');
    } catch (e) {
      NotificationLogger.error('Failed to initialize message handler: $e');
      rethrow;
    }
  }

  /// Handle incoming message (works for all app states)
  static Future<void> handleMessage(RemoteMessage message,
      {bool isForeground = false}) async {
    try {
      final firebaseId = message.messageId ??
          'unknown_${DateTime.now().millisecondsSinceEpoch}';

      // Detect app state for enhanced logging
      final appState = isForeground ? 'FOREGROUND' : 'BACKGROUND';
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      NotificationLogger.info(
          '🚀 HANDLER CALLED [$appState] at $timestamp: $firebaseId - ${message.notification?.title}');
      NotificationLogger.debug(
          '📊 HANDLER STATS: Firebase ID=$firebaseId, Timestamp=$timestamp, AppState=$appState');

      // Ensure handler is initialized
      await _ensureInitialized();

      // Create notification data
      final notificationData = NotificationData.fromRemoteMessage(message);

      // CRITICAL: For background/terminated, cancel only duplicate notifications
      if (!isForeground) {
        try {
          // Get active notifications to check for duplicates only
          final activeNotifications =
              await _localNotifications.getActiveNotifications();
          final notificationId =
              notificationData.id.hashCode.abs() % 2147483647;

          // Cancel only notifications with same ID or same content
          for (final active in activeNotifications) {
            if (active.id == notificationId ||
                (active.title == notificationData.displayTitle &&
                    active.body == notificationData.displayBody)) {
              await _localNotifications.cancel(active.id!);
              NotificationLogger.debug(
                  'Cancelled duplicate notification with ID: ${active.id}');
            }
          }

          // Small delay to ensure cancellation takes effect
          await Future.delayed(const Duration(milliseconds: 100));
        } catch (e) {
          NotificationLogger.debug(
              'Could not cancel duplicate notifications: $e');
        }
      }

      // Show the notification
      await _showNotification(notificationData);

      NotificationLogger.info(
          'Notification displayed successfully: ${notificationData.id}');
    } catch (e) {
      NotificationLogger.error('Error handling message: $e');
      // Don't show fallback notifications to avoid potential duplicates
    }
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    // Android initialization
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS initialization
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    await _createNotificationChannels();

    NotificationLogger.debug('Local notifications initialized');
  }

  /// Create notification channels for Android
  static Future<void> _createNotificationChannels() async {
    // Default channel
    const defaultChannel = AndroidNotificationChannel(
      NotificationConfig.defaultChannelId,
      NotificationConfig.defaultChannelName,
      description: NotificationConfig.defaultChannelDescription,
      importance: Importance.defaultImportance,
      enableVibration: NotificationConfig.enableVibration,
      enableLights: NotificationConfig.enableLights,
    );

    // High priority channel
    const highPriorityChannel = AndroidNotificationChannel(
      NotificationConfig.highPriorityChannelId,
      NotificationConfig.highPriorityChannelName,
      description: NotificationConfig.highPriorityChannelDescription,
      importance: Importance.high,
      enableVibration: NotificationConfig.enableVibration,
      enableLights: NotificationConfig.enableLights,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(defaultChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(highPriorityChannel);

    NotificationLogger.debug('Notification channels created');
  }

  /// Show notification using local notifications
  static Future<void> _showNotification(NotificationData notification) async {
    try {
      // Generate unique notification ID for display
      final displayId = notification.id.hashCode.abs() % 2147483647;

      // Note: Duplicate checking is now handled in the main handler for background/terminated
      // For foreground notifications, we still check to be safe
      try {
        final activeNotifications =
            await _localNotifications.getActiveNotifications();
        final isDuplicate = activeNotifications.any((active) =>
            active.id == displayId ||
            (active.title == notification.displayTitle &&
                active.body == notification.displayBody));

        if (isDuplicate) {
          NotificationLogger.debug(
              'Duplicate notification detected, skipping: ${notification.displayTitle}');
          return;
        }
      } catch (e) {
        NotificationLogger.debug('Could not check active notifications: $e');
      }

      // Determine channel based on notification priority
      final channelId = _getChannelId(notification);

      // Android notification details
      final androidDetails = AndroidNotificationDetails(
        channelId,
        _getChannelName(channelId),
        channelDescription: _getChannelDescription(channelId),
        importance: _getImportance(channelId),
        priority: Priority.high,
        enableVibration: NotificationConfig.enableVibration,
        enableLights: NotificationConfig.enableLights,
        // Note: Large icon from URL requires additional setup
        // For now, we'll use the default icon
      );

      // iOS notification details
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification
      await _localNotifications.show(
        displayId,
        notification.displayTitle,
        notification.displayBody,
        notificationDetails,
        payload: notification.id,
      );

      NotificationLogger.debug('Local notification shown with ID: $displayId');
    } catch (e) {
      NotificationLogger.error('Error showing notification: $e');
      rethrow;
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      final payload = response.payload;
      NotificationLogger.info('Notification tapped with payload: $payload');

      // Handle notification tap logic here
      // You can navigate to specific screens, update app state, etc.
    } catch (e) {
      NotificationLogger.error('Error handling notification tap: $e');
    }
  }

  /// Get appropriate channel ID based on notification content
  static String _getChannelId(NotificationData notification) {
    // You can implement logic to determine channel based on notification data
    // For now, use default channel
    return NotificationConfig.defaultChannelId;
  }

  /// Get channel name
  static String _getChannelName(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return NotificationConfig.highPriorityChannelName;
      default:
        return NotificationConfig.defaultChannelName;
    }
  }

  /// Get channel description
  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return NotificationConfig.highPriorityChannelDescription;
      default:
        return NotificationConfig.defaultChannelDescription;
    }
  }

  /// Get importance level
  static Importance _getImportance(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return Importance.high;
      default:
        return Importance.defaultImportance;
    }
  }

  /// Ensure handler is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Get handler status for diagnostics
  static Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'local_notifications_available': true,
      'channels_created': true,
      'handler_type': 'universal_message_handler',
    };
  }
}
