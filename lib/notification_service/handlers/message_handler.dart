import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/notification_data.dart';
import '../models/notification_config.dart';
import '../storage/notification_cache.dart';
import '../utils/content_hasher.dart';
import '../utils/logger.dart';

/// Universal message handler for all app states (foreground, background, terminated)
class MessageHandler {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  /// Initialize the message handler
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize local notifications
      await _initializeLocalNotifications();

      // Initialize cache
      await NotificationCache.initialize();

      _isInitialized = true;
      NotificationLogger.info('Message handler initialized successfully');
    } catch (e) {
      NotificationLogger.error('Failed to initialize message handler: $e');
      rethrow;
    }
  }

  /// Handle incoming message (works for all app states)
  static Future<void> handleMessage(RemoteMessage message) async {
    try {
      NotificationLogger.info(
          'Handling message: ${message.messageId} - ${message.notification?.title}');

      // Ensure handler is initialized
      await _ensureInitialized();

      // Create notification data
      final notificationData = NotificationData.fromRemoteMessage(message);

      // Generate content-based ID
      final contentId = ContentHasher.generateNotificationId(notificationData);
      final finalNotificationData = notificationData.copyWith(id: contentId);

      // Check for duplicates
      final shouldShow =
          await NotificationCache.shouldShowNotification(contentId);

      if (!shouldShow) {
        NotificationLogger.warning(
            'Duplicate notification blocked: $contentId');
        return;
      }

      // Show the notification
      await _showNotification(finalNotificationData);

      NotificationLogger.info(
          'Notification displayed successfully: $contentId');
    } catch (e) {
      NotificationLogger.error('Error handling message: $e');

      // In fail-safe mode, try to show notification anyway
      if (NotificationConfig.failSafeMode) {
        try {
          final fallbackData = NotificationData.fromRemoteMessage(message);
          await _showNotification(fallbackData.copyWith(
              id: 'fallback_${DateTime.now().millisecondsSinceEpoch}'));
          NotificationLogger.warning(
              'Showed fallback notification due to error');
        } catch (fallbackError) {
          NotificationLogger.error(
              'Fallback notification also failed: $fallbackError');
        }
      }
    }
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    // Android initialization
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS initialization
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    await _createNotificationChannels();

    NotificationLogger.debug('Local notifications initialized');
  }

  /// Create notification channels for Android
  static Future<void> _createNotificationChannels() async {
    // Default channel
    const defaultChannel = AndroidNotificationChannel(
      NotificationConfig.defaultChannelId,
      NotificationConfig.defaultChannelName,
      description: NotificationConfig.defaultChannelDescription,
      importance: Importance.defaultImportance,
      enableVibration: NotificationConfig.enableVibration,
      enableLights: NotificationConfig.enableLights,
    );

    // High priority channel
    const highPriorityChannel = AndroidNotificationChannel(
      NotificationConfig.highPriorityChannelId,
      NotificationConfig.highPriorityChannelName,
      description: NotificationConfig.highPriorityChannelDescription,
      importance: Importance.high,
      enableVibration: NotificationConfig.enableVibration,
      enableLights: NotificationConfig.enableLights,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(defaultChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(highPriorityChannel);

    NotificationLogger.debug('Notification channels created');
  }

  /// Show notification using local notifications
  static Future<void> _showNotification(NotificationData notification) async {
    try {
      // Generate unique notification ID for display
      final displayId = notification.id.hashCode.abs() % 2147483647;

      // Determine channel based on notification priority
      final channelId = _getChannelId(notification);

      // Android notification details
      final androidDetails = AndroidNotificationDetails(
        channelId,
        _getChannelName(channelId),
        channelDescription: _getChannelDescription(channelId),
        importance: _getImportance(channelId),
        priority: Priority.high,
        enableVibration: NotificationConfig.enableVibration,
        enableLights: NotificationConfig.enableLights,
        // Note: Large icon from URL requires additional setup
        // For now, we'll use the default icon
      );

      // iOS notification details
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification
      await _localNotifications.show(
        displayId,
        notification.displayTitle,
        notification.displayBody,
        notificationDetails,
        payload: notification.id,
      );

      NotificationLogger.debug('Local notification shown with ID: $displayId');
    } catch (e) {
      NotificationLogger.error('Error showing notification: $e');
      rethrow;
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      final payload = response.payload;
      NotificationLogger.info('Notification tapped with payload: $payload');

      // Handle notification tap logic here
      // You can navigate to specific screens, update app state, etc.
    } catch (e) {
      NotificationLogger.error('Error handling notification tap: $e');
    }
  }

  /// Get appropriate channel ID based on notification content
  static String _getChannelId(NotificationData notification) {
    // You can implement logic to determine channel based on notification data
    // For now, use default channel
    return NotificationConfig.defaultChannelId;
  }

  /// Get channel name
  static String _getChannelName(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return NotificationConfig.highPriorityChannelName;
      default:
        return NotificationConfig.defaultChannelName;
    }
  }

  /// Get channel description
  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return NotificationConfig.highPriorityChannelDescription;
      default:
        return NotificationConfig.defaultChannelDescription;
    }
  }

  /// Get importance level
  static Importance _getImportance(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return Importance.high;
      default:
        return Importance.defaultImportance;
    }
  }

  /// Ensure handler is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Get handler status for diagnostics
  static Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'local_notifications_available': true,
      'channels_created': true,
      'handler_type': 'universal_message_handler',
    };
  }
}
