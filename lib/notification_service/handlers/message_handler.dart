import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification_data.dart';
import '../models/notification_config.dart';
import '../utils/logger.dart';

/// Universal message handler for all app states (foreground, background, terminated)
class MessageHandler {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  /// Initialize the message handler
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize local notifications
      await _initializeLocalNotifications();

      _isInitialized = true;
      NotificationLogger.info('Message handler initialized successfully');
    } catch (e) {
      NotificationLogger.error('Failed to initialize message handler: $e');
      rethrow;
    }
  }

  /// Handle incoming message (works for all app states)
  static Future<void> handleMessage(RemoteMessage message,
      {bool isForeground = false}) async {
    try {
      final firebaseId = message.messageId ??
          'unknown_${DateTime.now().millisecondsSinceEpoch}';

      // Detect app state for enhanced logging
      final appState = isForeground ? 'FOREGROUND' : 'BACKGROUND';
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      NotificationLogger.info(
          '🚀 HANDLER CALLED [$appState] at $timestamp: $firebaseId - ${message.notification?.title}');
      NotificationLogger.debug(
          '📊 HANDLER STATS: Firebase ID=$firebaseId, Timestamp=$timestamp, AppState=$appState');

      // Ensure handler is initialized
      await _ensureInitialized();

      // Create notification data
      final notificationData = NotificationData.fromRemoteMessage(message);

      // Generate content-based ID for deduplication
      final contentId = _generateContentBasedId(notificationData);
      final finalNotificationData = notificationData.copyWith(id: contentId);

      // Apply deduplication logic based on app state
      if (isForeground) {
        NotificationLogger.debug(
            'App in foreground - skipping deduplication check');
        NotificationLogger.info(
            'NOTIFICATION APPROVED (FOREGROUND): $contentId');
      } else {
        // Check if we should show this notification (content-based deduplication for background/terminated only)
        final shouldShow = await _shouldShowNotification(contentId);

        if (!shouldShow) {
          NotificationLogger.warning(
              'Duplicate notification blocked: $contentId');
          return;
        }
        NotificationLogger.info(
            'NOTIFICATION APPROVED (BACKGROUND): $contentId');
      }

      // Show the notification
      await _showNotification(finalNotificationData);

      NotificationLogger.info(
          'Notification displayed successfully: $contentId');
    } catch (e) {
      NotificationLogger.error('Error handling message: $e');
      // Don't show fallback notifications to avoid potential duplicates
    }
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    // Android initialization
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS initialization
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    await _createNotificationChannels();

    NotificationLogger.debug('Local notifications initialized');
  }

  /// Create notification channels for Android
  static Future<void> _createNotificationChannels() async {
    // Default channel
    const defaultChannel = AndroidNotificationChannel(
      NotificationConfig.defaultChannelId,
      NotificationConfig.defaultChannelName,
      description: NotificationConfig.defaultChannelDescription,
      importance: Importance.defaultImportance,
      enableVibration: NotificationConfig.enableVibration,
      enableLights: NotificationConfig.enableLights,
    );

    // High priority channel
    const highPriorityChannel = AndroidNotificationChannel(
      NotificationConfig.highPriorityChannelId,
      NotificationConfig.highPriorityChannelName,
      description: NotificationConfig.highPriorityChannelDescription,
      importance: Importance.high,
      enableVibration: NotificationConfig.enableVibration,
      enableLights: NotificationConfig.enableLights,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(defaultChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(highPriorityChannel);

    NotificationLogger.debug('Notification channels created');
  }

  /// Show notification using local notifications
  static Future<void> _showNotification(NotificationData notification) async {
    try {
      // Generate unique notification ID for display
      final displayId = notification.id.hashCode.abs() % 2147483647;

      // Determine channel based on notification priority
      final channelId = _getChannelId(notification);

      // Android notification details
      final androidDetails = AndroidNotificationDetails(
        channelId,
        _getChannelName(channelId),
        channelDescription: _getChannelDescription(channelId),
        importance: _getImportance(channelId),
        priority: Priority.high,
        enableVibration: NotificationConfig.enableVibration,
        enableLights: NotificationConfig.enableLights,
        // Note: Large icon from URL requires additional setup
        // For now, we'll use the default icon
      );

      // iOS notification details
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification
      await _localNotifications.show(
        displayId,
        notification.displayTitle,
        notification.displayBody,
        notificationDetails,
        payload: notification.id,
      );

      NotificationLogger.debug('Local notification shown with ID: $displayId');
    } catch (e) {
      NotificationLogger.error('Error showing notification: $e');
      rethrow;
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      final payload = response.payload;
      NotificationLogger.info('Notification tapped with payload: $payload');

      // Handle notification tap logic here
      // You can navigate to specific screens, update app state, etc.
    } catch (e) {
      NotificationLogger.error('Error handling notification tap: $e');
    }
  }

  /// Get appropriate channel ID based on notification content
  static String _getChannelId(NotificationData notification) {
    // You can implement logic to determine channel based on notification data
    // For now, use default channel
    return NotificationConfig.defaultChannelId;
  }

  /// Get channel name
  static String _getChannelName(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return NotificationConfig.highPriorityChannelName;
      default:
        return NotificationConfig.defaultChannelName;
    }
  }

  /// Get channel description
  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return NotificationConfig.highPriorityChannelDescription;
      default:
        return NotificationConfig.defaultChannelDescription;
    }
  }

  /// Get importance level
  static Importance _getImportance(String channelId) {
    switch (channelId) {
      case NotificationConfig.highPriorityChannelId:
        return Importance.high;
      default:
        return Importance.defaultImportance;
    }
  }

  /// Ensure handler is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Generate content-based ID for deduplication
  static String _generateContentBasedId(NotificationData notification) {
    try {
      // Create content string for hashing (same logic as before)
      final contentParts = [
        notification.title,
        notification.body,
        _extractStableDataString(notification.data),
      ];

      final content = contentParts.join('|');

      // Generate hash
      final bytes = utf8.encode(content);
      final digest = sha256.convert(bytes);
      final hashedId = digest.toString().substring(0, 12);

      NotificationLogger.debug(
          'Generated content-based ID: $hashedId for notification: "${notification.title}" '
          '(Firebase ID: ${notification.firebaseMessageId})');

      // DETAILED DEBUGGING: Show exactly what content is being hashed
      NotificationLogger.debug(
          'CONTENT HASH DEBUG: Title="${notification.title}" Body="${notification.body}" Data=${notification.data}');
      NotificationLogger.debug(
          'CONTENT HASH DEBUG: Full content string for hashing: "$content"');

      return hashedId;
    } catch (e) {
      NotificationLogger.error('Error generating content-based ID: $e');
      // Fallback to Firebase message ID
      return notification.firebaseMessageId ??
          'fallback_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Extract stable data string (ignoring timestamp-based fields)
  static String _extractStableDataString(Map<String, dynamic> data) {
    final stableData = <String, dynamic>{};

    // Fields to ignore (timestamp-based or ID-based)
    final fieldsToIgnore = {
      'timestamp',
      'sent_time',
      'created_at',
      'updated_at',
      'id',
      'uuid',
      'message_id',
      'notification_id'
    };

    for (final entry in data.entries) {
      if (!fieldsToIgnore.contains(entry.key.toLowerCase())) {
        stableData[entry.key] = entry.value;
      }
    }

    // Sort keys for consistent hashing
    final sortedKeys = stableData.keys.toList()..sort();
    final sortedPairs =
        sortedKeys.map((key) => '$key:${stableData[key]}').toList();

    return sortedPairs.join(',');
  }

  /// Check if notification should be shown (content-based deduplication)
  static Future<bool> _shouldShowNotification(String contentId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'notif_$contentId';
      final now = DateTime.now().millisecondsSinceEpoch;

      // Clean up expired notifications periodically (every 100 notifications)
      await _cleanupExpiredNotifications(prefs, now);

      // Check if this content ID exists and is still valid
      final existingTimestamp = prefs.getInt(key);
      if (existingTimestamp != null) {
        final expiryTime = existingTimestamp + (5 * 1000); // 5 seconds
        if (now < expiryTime) {
          final remainingSeconds = ((expiryTime - now) / 1000).ceil();
          NotificationLogger.warning(
              'DUPLICATE BLOCKED: $contentId (expires in ${remainingSeconds}s)');
          return false;
        }
      }

      // Record this notification
      await prefs.setInt(key, now);
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(now + (5 * 1000));
      final expiryString =
          '${expiryTime.hour.toString().padLeft(2, '0')}:${expiryTime.minute.toString().padLeft(2, '0')}:${expiryTime.second.toString().padLeft(2, '0')}';

      NotificationLogger.debug(
          'Notification recorded: $contentId (expires: $expiryString)');

      return true;
    } catch (e) {
      NotificationLogger.error('Error checking notification deduplication: $e');
      // In case of error, allow the notification (fail-safe)
      return true;
    }
  }

  /// Clean up expired notifications to prevent storage bloat
  static Future<void> _cleanupExpiredNotifications(
      SharedPreferences prefs, int now) async {
    try {
      // Only cleanup every 100th call to avoid performance impact
      final cleanupCounter = prefs.getInt('cleanup_counter') ?? 0;
      if (cleanupCounter % 100 != 0) {
        await prefs.setInt('cleanup_counter', cleanupCounter + 1);
        return;
      }

      final keys =
          prefs.getKeys().where((key) => key.startsWith('notif_')).toList();
      final expiredKeys = <String>[];

      for (final key in keys) {
        try {
          final timestamp = prefs.getInt(key);
          if (timestamp != null) {
            final expiryTime = timestamp + (5 * 1000); // 5 seconds
            if (now >= expiryTime) {
              expiredKeys.add(key);
            }
          }
        } catch (e) {
          // If there's an error reading this key, mark it for removal
          expiredKeys.add(key);
          NotificationLogger.debug('Marking corrupted key for removal: $key');
        }
      }

      // Remove expired notifications
      for (final key in expiredKeys) {
        await prefs.remove(key);
      }

      if (expiredKeys.isNotEmpty) {
        NotificationLogger.debug(
            'Cleaned up ${expiredKeys.length} expired notifications');
      }

      await prefs.setInt('cleanup_counter', cleanupCounter + 1);
    } catch (e) {
      NotificationLogger.error('Error cleaning up expired notifications: $e');
    }
  }

  /// Get handler status for diagnostics
  static Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'local_notifications_available': true,
      'channels_created': true,
      'handler_type': 'universal_message_handler',
    };
  }
}
