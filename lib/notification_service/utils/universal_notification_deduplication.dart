import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

/// Universal notification deduplication system that works across ALL app states
/// - Foreground, background, and terminated
/// - Uses only persistent storage for maximum reliability
/// - Single source of truth for all notification deduplication
class UniversalNotificationDeduplication {
  static const Duration _duplicateWindow = Duration(minutes: 20); // Extended window
  static const String _keyPrefix = 'universal_notif_';
  static const String _expiryPrefix = 'universal_notif_exp_';
  
  /// Generate consistent message ID across ALL contexts (foreground, background, terminated)
  /// This is the SINGLE source of truth for message ID generation
  static String generateMessageId(RemoteMessage message) {
    // Priority 1: Use Firebase message ID if available
    if (message.messageId != null && message.messageId!.isNotEmpty) {
      return message.messageId!;
    }
    
    // Priority 2: Create deterministic ID from message content
    // Use ALL available message properties for maximum uniqueness
    final components = [
      message.notification?.title ?? '',
      message.notification?.body ?? '',
      message.data.toString(),
      message.sentTime?.millisecondsSinceEpoch.toString() ?? '',
      message.from ?? '',
      message.collapseKey ?? '',
    ];
    
    final contentHash = components.join('|');
    final messageId = contentHash.hashCode.abs().toString();
    
    Logger.firebase('Generated universal message ID: $messageId for message: ${message.notification?.title}');
    return messageId;
  }
  
  /// Check if notification should be shown - works in ALL app states
  static Future<bool> shouldShowNotification(String messageId) async {
    try {
      // Ensure cache is initialized with robust retry mechanism
      await _ensureCacheInitialized();
      
      final now = DateTime.now();
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      
      Logger.firebase('Universal dedup check for: $messageId');
      
      // Check if notification was already shown
      final isShown = CacheHelper.getBool(cacheKey) ?? false;
      
      if (isShown) {
        // Check if still within duplicate window
        final expiryTime = CacheHelper.getInt(expiryKey);
        
        if (expiryTime != null) {
          final isExpired = expiryTime < now.millisecondsSinceEpoch;
          
          if (!isExpired) {
            final timeLeft = Duration(milliseconds: expiryTime - now.millisecondsSinceEpoch);
            Logger.w('DUPLICATE BLOCKED: $messageId (expires in ${timeLeft.inMinutes}m ${timeLeft.inSeconds % 60}s)');
            return false;
          } else {
            // Clean up expired entry
            await _cleanupEntry(messageId);
            Logger.i('Expired entry cleaned for: $messageId');
          }
        } else {
          // Orphaned entry, clean it up
          await CacheHelper.remove(cacheKey);
          Logger.w('Orphaned entry cleaned for: $messageId');
        }
      }
      
      // Record this notification
      await _recordNotification(messageId, now);
      
      Logger.i('NOTIFICATION APPROVED: $messageId');
      return true;
      
    } catch (e) {
      Logger.e('Error in universal deduplication for $messageId: $e');
      // Fail safe - allow notification to prevent missing important messages
      return true;
    }
  }
  
  /// Robust cache initialization with retry mechanism
  static Future<void> _ensureCacheInitialized() async {
    int retries = 0;
    const maxRetries = 5;
    
    while (retries < maxRetries) {
      try {
        await CacheHelper.init();
        
        // Test cache functionality
        const testKey = 'universal_dedup_test';
        const testValue = 'test_value';
        
        await CacheHelper.putString(testKey, testValue);
        final retrievedValue = CacheHelper.getString(testKey);
        await CacheHelper.remove(testKey);
        
        if (retrievedValue == testValue) {
          if (retries > 0) {
            Logger.i('Universal cache initialized successfully after $retries retries');
          }
          return;
        }
        
        throw Exception('Cache test failed - retrieved: $retrievedValue, expected: $testValue');
        
      } catch (e) {
        retries++;
        Logger.w('Universal cache init attempt $retries/$maxRetries failed: $e');
        
        if (retries < maxRetries) {
          // Exponential backoff: 50ms, 100ms, 200ms, 400ms
          final delayMs = 50 * (1 << (retries - 1));
          await Future.delayed(Duration(milliseconds: delayMs));
        } else {
          throw Exception('Failed to initialize universal cache after $maxRetries attempts: $e');
        }
      }
    }
  }
  
  /// Record notification in persistent storage
  static Future<void> _recordNotification(String messageId, DateTime now) async {
    try {
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      final expiryTime = now.add(_duplicateWindow).millisecondsSinceEpoch;
      
      // Atomic operations to prevent race conditions
      await CacheHelper.putBool(cacheKey, true);
      await CacheHelper.putInt(expiryKey, expiryTime);
      
      final expiryDateTime = DateTime.fromMillisecondsSinceEpoch(expiryTime);
      Logger.firebase('Universal notification recorded: $messageId (expires: ${expiryDateTime.toString().substring(11, 19)})');
      
    } catch (e) {
      Logger.e('Error recording universal notification $messageId: $e');
      rethrow;
    }
  }
  
  /// Clean up specific entry
  static Future<void> _cleanupEntry(String messageId) async {
    try {
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      
      await CacheHelper.remove(cacheKey);
      await CacheHelper.remove(expiryKey);
      
    } catch (e) {
      Logger.e('Error cleaning up universal entry $messageId: $e');
    }
  }
  
  /// Clean up all expired entries
  static Future<void> cleanupExpiredEntries() async {
    try {
      await _ensureCacheInitialized();
      
      final now = DateTime.now().millisecondsSinceEpoch;
      final keys = CacheHelper.getKeys();
      int cleanedCount = 0;
      
      for (final key in keys) {
        if (key.startsWith(_expiryPrefix)) {
          final expiryTime = CacheHelper.getInt(key);
          if (expiryTime != null && expiryTime < now) {
            final messageId = key.replaceFirst(_expiryPrefix, '');
            await _cleanupEntry(messageId);
            cleanedCount++;
          }
        }
      }
      
      if (cleanedCount > 0) {
        Logger.i('Universal dedup: Cleaned up $cleanedCount expired entries');
      }
      
    } catch (e) {
      Logger.e('Error cleaning up universal notification cache: $e');
    }
  }
  
  /// Get comprehensive statistics
  static Future<Map<String, dynamic>> getStats() async {
    try {
      await _ensureCacheInitialized();
      
      final keys = CacheHelper.getKeys();
      final notificationKeys = keys.where((key) => key.startsWith(_keyPrefix)).length;
      final expiryKeys = keys.where((key) => key.startsWith(_expiryPrefix)).length;
      
      final now = DateTime.now().millisecondsSinceEpoch;
      int activeCount = 0;
      int expiredCount = 0;
      
      for (final key in keys) {
        if (key.startsWith(_expiryPrefix)) {
          final expiryTime = CacheHelper.getInt(key);
          if (expiryTime != null) {
            if (expiryTime > now) {
              activeCount++;
            } else {
              expiredCount++;
            }
          }
        }
      }
      
      return {
        'total_notification_entries': notificationKeys,
        'total_expiry_entries': expiryKeys,
        'active_entries': activeCount,
        'expired_entries': expiredCount,
        'duplicate_window_minutes': _duplicateWindow.inMinutes,
        'cache_prefix': _keyPrefix,
        'system_type': 'universal',
      };
      
    } catch (e) {
      Logger.e('Error getting universal dedup stats: $e');
      return {'error': e.toString()};
    }
  }
  
  /// Clear all entries (for testing/debugging)
  static Future<void> clearAll() async {
    try {
      await _ensureCacheInitialized();
      
      final keys = CacheHelper.getKeys();
      int removedCount = 0;
      
      for (final key in keys) {
        if (key.startsWith(_keyPrefix) || key.startsWith(_expiryPrefix)) {
          await CacheHelper.remove(key);
          removedCount++;
        }
      }
      
      Logger.i('Universal dedup: Cleared $removedCount cache entries');
      
    } catch (e) {
      Logger.e('Error clearing universal notification cache: $e');
    }
  }
  
  /// Check if specific message is cached (for debugging)
  static Future<bool> isMessageCached(String messageId) async {
    try {
      await _ensureCacheInitialized();
      
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      
      final isShown = CacheHelper.getBool(cacheKey) ?? false;
      if (!isShown) return false;
      
      final expiryTime = CacheHelper.getInt(expiryKey);
      if (expiryTime == null) return false;
      
      final now = DateTime.now().millisecondsSinceEpoch;
      return expiryTime > now;
      
    } catch (e) {
      Logger.e('Error checking if message is cached: $e');
      return false;
    }
  }
}
