import 'package:firebase_messaging/firebase_messaging.dart';
import '../notification_manager.dart';
import '../storage/notification_cache.dart';
import '../handlers/message_handler.dart';
import '../models/notification_config.dart';
import 'logger.dart';

/// Comprehensive diagnostic tools for the notification system
class NotificationDiagnostics {
  
  /// Run complete system diagnostics
  static Future<Map<String, dynamic>> runFullDiagnostics() async {
    final results = <String, dynamic>{};
    
    NotificationLogger.info('=== STARTING NOTIFICATION SYSTEM DIAGNOSTICS ===');
    
    try {
      // 1. System Status
      results['system_status'] = await _checkSystemStatus();
      
      // 2. FCM Token Status
      results['fcm_token'] = await _checkFCMToken();
      
      // 3. Permissions
      results['permissions'] = await _checkPermissions();
      
      // 4. Cache System
      results['cache_system'] = await _checkCacheSystem();
      
      // 5. Message Handler
      results['message_handler'] = _checkMessageHandler();
      
      // 6. Performance Metrics
      results['performance'] = await _checkPerformanceMetrics();
      
      // 7. Configuration
      results['configuration'] = _checkConfiguration();
      
      NotificationLogger.info('=== DIAGNOSTICS COMPLETE ===');
      _printDiagnosticsSummary(results);
      
      return results;
      
    } catch (e) {
      NotificationLogger.error('Error running diagnostics: $e');
      results['error'] = e.toString();
      return results;
    }
  }
  
  /// Check overall system status
  static Future<Map<String, dynamic>> _checkSystemStatus() async {
    final status = <String, dynamic>{};
    
    try {
      status['notification_manager_initialized'] = NotificationManager.isInitialized;
      status['system_status'] = await NotificationManager.getSystemStatus();
      status['status'] = 'success';
      
    } catch (e) {
      status['status'] = 'error';
      status['error'] = e.toString();
    }
    
    return status;
  }
  
  /// Check FCM token status
  static Future<Map<String, dynamic>> _checkFCMToken() async {
    final tokenInfo = <String, dynamic>{};
    
    try {
      // Get current token
      final token = await NotificationManager.getFCMToken();
      tokenInfo['token_available'] = token != null;
      tokenInfo['token_length'] = token?.length ?? 0;
      
      // Validate token format
      if (token != null) {
        tokenInfo['token_format_valid'] = token.length > 100 && 
                                         token.contains(':') && 
                                         !token.contains(' ');
      }
      
      // Check permissions
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.getNotificationSettings();
      tokenInfo['authorization_status'] = settings.authorizationStatus.toString();
      
      tokenInfo['status'] = 'success';
      
    } catch (e) {
      tokenInfo['status'] = 'error';
      tokenInfo['error'] = e.toString();
    }
    
    return tokenInfo;
  }
  
  /// Check notification permissions
  static Future<Map<String, dynamic>> _checkPermissions() async {
    final permissionInfo = <String, dynamic>{};
    
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.getNotificationSettings();
      
      permissionInfo['authorization_status'] = settings.authorizationStatus.toString();
      permissionInfo['alert_setting'] = settings.alert.toString();
      permissionInfo['badge_setting'] = settings.badge.toString();
      permissionInfo['sound_setting'] = settings.sound.toString();
      permissionInfo['is_authorized'] = settings.authorizationStatus == AuthorizationStatus.authorized;
      
      permissionInfo['status'] = 'success';
      
    } catch (e) {
      permissionInfo['status'] = 'error';
      permissionInfo['error'] = e.toString();
    }
    
    return permissionInfo;
  }
  
  /// Check cache system functionality
  static Future<Map<String, dynamic>> _checkCacheSystem() async {
    final cacheInfo = <String, dynamic>{};
    
    try {
      // Get cache statistics
      final stats = await NotificationCache.getStats();
      cacheInfo['cache_stats'] = stats;
      
      // Test cache functionality
      final testId = 'diagnostic_test_${DateTime.now().millisecondsSinceEpoch}';
      
      // Test deduplication
      final shouldShow1 = await NotificationCache.shouldShowNotification(testId);
      final shouldShow2 = await NotificationCache.shouldShowNotification(testId);
      
      cacheInfo['deduplication_working'] = shouldShow1 && !shouldShow2;
      
      // Clean up test data
      await NotificationCache.clearAll();
      
      cacheInfo['status'] = 'success';
      
    } catch (e) {
      cacheInfo['status'] = 'error';
      cacheInfo['error'] = e.toString();
    }
    
    return cacheInfo;
  }
  
  /// Check message handler status
  static Map<String, dynamic> _checkMessageHandler() {
    final handlerInfo = <String, dynamic>{};
    
    try {
      handlerInfo.addAll(MessageHandler.getStatus());
      handlerInfo['status'] = 'success';
      
    } catch (e) {
      handlerInfo['status'] = 'error';
      handlerInfo['error'] = e.toString();
    }
    
    return handlerInfo;
  }
  
  /// Check performance metrics
  static Future<Map<String, dynamic>> _checkPerformanceMetrics() async {
    final perfInfo = <String, dynamic>{};
    
    try {
      // Measure token retrieval time
      final stopwatch = Stopwatch()..start();
      await NotificationManager.getFCMToken();
      stopwatch.stop();
      perfInfo['token_retrieval_ms'] = stopwatch.elapsedMilliseconds;
      
      // Measure cache operation time
      stopwatch.reset();
      stopwatch.start();
      final testId = 'perf_test_${DateTime.now().millisecondsSinceEpoch}';
      await NotificationCache.shouldShowNotification(testId);
      stopwatch.stop();
      perfInfo['cache_operation_ms'] = stopwatch.elapsedMilliseconds;
      
      // Clean up
      await NotificationCache.clearAll();
      
      perfInfo['status'] = 'success';
      
    } catch (e) {
      perfInfo['status'] = 'error';
      perfInfo['error'] = e.toString();
    }
    
    return perfInfo;
  }
  
  /// Check configuration
  static Map<String, dynamic> _checkConfiguration() {
    return {
      'deduplication_window_minutes': NotificationConfig.deduplicationWindow.inMinutes,
      'rapid_fire_window_seconds': NotificationConfig.rapidFireWindow.inSeconds,
      'max_cache_entries': NotificationConfig.maxCacheEntries,
      'fail_safe_mode': NotificationConfig.failSafeMode,
      'debug_logging': NotificationConfig.enableDebugLogging,
      'auto_initialize': NotificationConfig.autoInitializeOnAppStart,
      'config_valid': NotificationConfig.validateConfig(),
    };
  }
  
  /// Print diagnostic summary
  static void _printDiagnosticsSummary(Map<String, dynamic> results) {
    NotificationLogger.info('=== NOTIFICATION SYSTEM DIAGNOSTIC SUMMARY ===');
    
    // System Status
    final systemStatus = results['system_status'] as Map<String, dynamic>?;
    final isInitialized = systemStatus?['notification_manager_initialized'] == true;
    NotificationLogger.info('System Status: ${isInitialized ? "✅ INITIALIZED" : "❌ NOT INITIALIZED"}');
    
    // FCM Token
    final fcmToken = results['fcm_token'] as Map<String, dynamic>?;
    final tokenAvailable = fcmToken?['token_available'] == true;
    NotificationLogger.info('FCM Token: ${tokenAvailable ? "✅ AVAILABLE" : "❌ NOT AVAILABLE"}');
    
    // Permissions
    final permissions = results['permissions'] as Map<String, dynamic>?;
    final isAuthorized = permissions?['is_authorized'] == true;
    NotificationLogger.info('Permissions: ${isAuthorized ? "✅ AUTHORIZED" : "❌ NOT AUTHORIZED"}');
    
    // Cache System
    final cacheSystem = results['cache_system'] as Map<String, dynamic>?;
    final dedupWorking = cacheSystem?['deduplication_working'] == true;
    NotificationLogger.info('Deduplication: ${dedupWorking ? "✅ WORKING" : "❌ FAILED"}');
    
    // Performance
    final performance = results['performance'] as Map<String, dynamic>?;
    if (performance?['status'] == 'success') {
      final tokenTime = performance?['token_retrieval_ms'] ?? 0;
      final cacheTime = performance?['cache_operation_ms'] ?? 0;
      NotificationLogger.info('Performance: Token: ${tokenTime}ms, Cache: ${cacheTime}ms');
    }
    
    NotificationLogger.info('=== END DIAGNOSTIC SUMMARY ===');
  }
  
  /// Test notification deduplication with specific scenario
  static Future<bool> testDeduplicationScenario() async {
    try {
      NotificationLogger.info('Testing deduplication scenario...');
      
      // Clear any existing data
      await NotificationCache.clearAll();
      
      // Test identical content with different Firebase IDs
      final testId1 = 'content_test_1';
      final testId2 = 'content_test_1'; // Same content-based ID
      
      // First notification should be allowed
      final result1 = await NotificationCache.shouldShowNotification(testId1);
      if (!result1) {
        throw Exception('First notification was blocked');
      }
      
      // Second identical notification should be blocked
      final result2 = await NotificationCache.shouldShowNotification(testId2);
      if (result2) {
        throw Exception('Duplicate notification was not blocked');
      }
      
      // Different content should be allowed
      final testId3 = 'content_test_2';
      final result3 = await NotificationCache.shouldShowNotification(testId3);
      if (!result3) {
        throw Exception('Different notification was blocked');
      }
      
      // Clean up
      await NotificationCache.clearAll();
      
      NotificationLogger.info('Deduplication scenario test passed ✅');
      return true;
      
    } catch (e) {
      NotificationLogger.error('Deduplication scenario test failed: $e');
      return false;
    }
  }
}
