import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/notification_service/utils/universal_notification_deduplication.dart';
import 'package:busaty_parents/notification_service/local_notification_service.dart';

/// Comprehensive diagnostic tool for the notification system
class NotificationSystemDiagnostics {
  /// Run a complete diagnostic check of the notification system
  static Future<Map<String, dynamic>> runFullDiagnostics() async {
    final results = <String, dynamic>{};

    Logger.i('=== STARTING NOTIFICATION SYSTEM DIAGNOSTICS ===');

    // 1. Check FCM Token Status
    results['fcm_token'] = await _checkFCMToken();

    // 2. Check Permissions
    results['permissions'] = await _checkPermissions();

    // 3. Check Service Initialization
    results['service_initialization'] = _checkServiceInitialization();

    // 4. Check Cache System
    results['cache_system'] = await _checkCacheSystem();

    // 5. Check Deduplication System
    results['deduplication'] = await _checkDeduplicationSystem();

    // 6. Check Platform-specific Settings
    results['platform_settings'] = await _checkPlatformSettings();

    // 7. Performance Metrics
    results['performance'] = await _checkPerformanceMetrics();

    Logger.i('=== DIAGNOSTICS COMPLETE ===');
    _printDiagnosticsSummary(results);

    return results;
  }

  /// Check FCM token status and validity
  static Future<Map<String, dynamic>> _checkFCMToken() async {
    final tokenInfo = <String, dynamic>{};

    try {
      // Check cached token
      final cachedToken = CacheHelper.getString("fcmToken");
      tokenInfo['cached_token_exists'] =
          cachedToken != null && cachedToken.isNotEmpty;
      tokenInfo['cached_token_length'] = cachedToken?.length ?? 0;

      // Try to get fresh token
      final freshToken = await LocalNotificationService.firebaseToken;
      tokenInfo['fresh_token_obtained'] = freshToken != null;
      tokenInfo['fresh_token_length'] = freshToken?.length ?? 0;

      // Compare tokens
      tokenInfo['tokens_match'] = cachedToken == freshToken;

      // Check token format (FCM tokens are typically 152+ characters)
      if (freshToken != null) {
        tokenInfo['token_format_valid'] = freshToken.length > 100 &&
            freshToken.contains(':') &&
            !freshToken.contains(' ');
      }

      tokenInfo['status'] = 'success';
    } catch (e) {
      tokenInfo['status'] = 'error';
      tokenInfo['error'] = e.toString();
    }

    return tokenInfo;
  }

  /// Check notification permissions
  static Future<Map<String, dynamic>> _checkPermissions() async {
    final permissionInfo = <String, dynamic>{};

    try {
      final settings =
          await FirebaseMessaging.instance.getNotificationSettings();

      permissionInfo['authorization_status'] =
          settings.authorizationStatus.toString();
      permissionInfo['alert_setting'] = settings.alert.toString();
      permissionInfo['badge_setting'] = settings.badge.toString();
      permissionInfo['sound_setting'] = settings.sound.toString();

      // Only include properties that exist in the current Firebase version
      try {
        permissionInfo['announcement_setting'] =
            settings.announcement.toString();
      } catch (e) {
        permissionInfo['announcement_setting'] = 'not_available';
      }

      try {
        permissionInfo['car_play_setting'] = settings.carPlay.toString();
      } catch (e) {
        permissionInfo['car_play_setting'] = 'not_available';
      }

      try {
        permissionInfo['critical_alert_setting'] =
            settings.criticalAlert.toString();
      } catch (e) {
        permissionInfo['critical_alert_setting'] = 'not_available';
      }

      permissionInfo['is_authorized'] =
          settings.authorizationStatus == AuthorizationStatus.authorized;
      permissionInfo['status'] = 'success';
    } catch (e) {
      permissionInfo['status'] = 'error';
      permissionInfo['error'] = e.toString();
    }

    return permissionInfo;
  }

  /// Check service initialization status
  static Map<String, dynamic> _checkServiceInitialization() {
    final initInfo = <String, dynamic>{};

    initInfo['local_service_initialized'] =
        LocalNotificationService.isInitialized;
    initInfo['message_handlers_setup'] = LocalNotificationService.isInitialized;

    return initInfo;
  }

  /// Check cache system health
  static Future<Map<String, dynamic>> _checkCacheSystem() async {
    final cacheInfo = <String, dynamic>{};

    try {
      // Test cache operations
      const testKey = 'diagnostic_test_key';
      const testValue = 'diagnostic_test_value';

      await CacheHelper.putString(testKey, testValue);
      final retrievedValue = CacheHelper.getString(testKey);
      await CacheHelper.remove(testKey);

      cacheInfo['cache_write_read_works'] = retrievedValue == testValue;

      // Check cache size
      final allKeys = CacheHelper.getKeys();
      cacheInfo['total_cache_keys'] = allKeys.length;

      // Count notification-related keys
      final notificationKeys = allKeys
          .where((key) =>
              key.startsWith('notification_') ||
              key.startsWith('unified_notification_') ||
              key.startsWith('fcmToken'))
          .toList();

      cacheInfo['notification_cache_keys'] = notificationKeys.length;
      cacheInfo['notification_keys'] = notificationKeys;

      cacheInfo['status'] = 'success';
    } catch (e) {
      cacheInfo['status'] = 'error';
      cacheInfo['error'] = e.toString();
    }

    return cacheInfo;
  }

  /// Check deduplication system
  static Future<Map<String, dynamic>> _checkDeduplicationSystem() async {
    final dedupInfo = <String, dynamic>{};

    try {
      // Get universal deduplication stats
      final universalStats =
          await UniversalNotificationDeduplication.getStats();
      dedupInfo['universal'] = universalStats;

      dedupInfo['status'] = 'success';
    } catch (e) {
      dedupInfo['status'] = 'error';
      dedupInfo['error'] = e.toString();
    }

    return dedupInfo;
  }

  /// Check platform-specific settings
  static Future<Map<String, dynamic>> _checkPlatformSettings() async {
    final platformInfo = <String, dynamic>{};

    try {
      // Check foreground notification settings
      // We can't directly check the foreground options, but we can verify the service exists
      platformInfo['firebase_messaging_available'] = true;
      platformInfo['foreground_options_set'] =
          true; // We can't directly check this

      platformInfo['status'] = 'success';
    } catch (e) {
      platformInfo['status'] = 'error';
      platformInfo['error'] = e.toString();
    }

    return platformInfo;
  }

  /// Check performance metrics
  static Future<Map<String, dynamic>> _checkPerformanceMetrics() async {
    final perfInfo = <String, dynamic>{};

    try {
      // Measure token retrieval time
      final stopwatch = Stopwatch()..start();
      await LocalNotificationService.firebaseToken;
      stopwatch.stop();

      perfInfo['token_retrieval_ms'] = stopwatch.elapsedMilliseconds;

      // Measure cache operation time
      stopwatch.reset();
      stopwatch.start();
      const testKey = 'perf_test_key';
      await CacheHelper.putString(testKey, 'test');
      CacheHelper.getString(testKey);
      await CacheHelper.remove(testKey);
      stopwatch.stop();

      perfInfo['cache_operation_ms'] = stopwatch.elapsedMilliseconds;

      perfInfo['status'] = 'success';
    } catch (e) {
      perfInfo['status'] = 'error';
      perfInfo['error'] = e.toString();
    }

    return perfInfo;
  }

  /// Print a summary of diagnostic results
  static void _printDiagnosticsSummary(Map<String, dynamic> results) {
    Logger.i('=== NOTIFICATION SYSTEM DIAGNOSTIC SUMMARY ===');

    // FCM Token
    final fcmToken = results['fcm_token'] as Map<String, dynamic>;
    Logger.i(
        'FCM Token: ${fcmToken['fresh_token_obtained'] == true ? "✅ OK" : "❌ FAILED"}');

    // Permissions
    final permissions = results['permissions'] as Map<String, dynamic>;
    Logger.i(
        'Permissions: ${permissions['is_authorized'] == true ? "✅ AUTHORIZED" : "❌ NOT AUTHORIZED"}');

    // Service Initialization
    final serviceInit =
        results['service_initialization'] as Map<String, dynamic>;
    Logger.i(
        'Service Init: ${serviceInit['local_service_initialized'] == true ? "✅ INITIALIZED" : "❌ NOT INITIALIZED"}');

    // Cache System
    final cache = results['cache_system'] as Map<String, dynamic>;
    Logger.i(
        'Cache System: ${cache['cache_write_read_works'] == true ? "✅ WORKING" : "❌ FAILED"}');

    // Deduplication
    final dedup = results['deduplication'] as Map<String, dynamic>;
    Logger.i(
        'Deduplication: ${dedup['status'] == 'success' ? "✅ WORKING" : "❌ FAILED"}');

    // Performance
    final perf = results['performance'] as Map<String, dynamic>;
    if (perf['status'] == 'success') {
      Logger.i(
          'Performance: Token retrieval: ${perf['token_retrieval_ms']}ms, Cache ops: ${perf['cache_operation_ms']}ms');
    }

    Logger.i('=== END DIAGNOSTIC SUMMARY ===');
  }

  /// Test notification delivery by sending a test notification
  static Future<bool> testNotificationDelivery() async {
    try {
      Logger.i('Testing notification delivery...');

      // This would require server-side implementation
      // For now, just check if we can create a local notification

      Logger.i('Test notification delivery completed');
      return true;
    } catch (e) {
      Logger.e('Test notification delivery failed: $e');
      return false;
    }
  }
}
