import 'package:flutter/material.dart';

class Logger {
  static void d(String message) {
    debugPrint('💡 DEBUG: $message');
  }

  static void i(String message) {
    debugPrint('ℹ️ INFO: $message');
  }

  static void w(String message) {
    debugPrint('⚠️ WARN: $message');
  }

  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    debugPrint('❌ ERROR: $message');
    if (error != null) {
      debugPrint('Error details: $error');
    }
    if (stackTrace != null) {
      debugPrint('Stack trace: $stackTrace');
    }
  }

  static void api(String message) {
    debugPrint('🌐 API: $message');
  }

  static void firebase(String message) {
    debugPrint('🔥 FIREBASE: $message');
  }
}

/// Notification-specific logger with enhanced formatting
class NotificationLogger {
  static void debug(String message) {
    debugPrint('🔔 NOTIF DEBUG: $message');
  }

  static void info(String message) {
    debugPrint('🔔 NOTIF INFO: $message');
  }

  static void warning(String message) {
    debugPrint('🔔 NOTIF WARN: $message');
  }

  static void error(String message) {
    debugPrint('🔔 NOTIF ERROR: $message');
  }
}
