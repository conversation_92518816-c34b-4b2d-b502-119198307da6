import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';

/// Background-specific notification deduplication system
/// Designed specifically for background/terminated app contexts where memory cache is not reliable
class BackgroundNotificationDeduplication {
  static const Duration _duplicateWindow = Duration(minutes: 15); // Longer window for background
  static const String _keyPrefix = 'bg_notification_';
  static const String _expiryPrefix = 'bg_notification_expiry_';
  
  /// Check if a notification should be shown in background context
  /// Returns true if the notification should be shown, false if it's a duplicate
  /// This method is specifically designed for background handlers
  static Future<bool> shouldShowNotification(String messageId) async {
    try {
      // Ensure cache is properly initialized with retry mechanism
      await _ensureCacheInitialized();
      
      final now = DateTime.now();
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      
      Logger.firebase('Background dedup check for: $messageId');
      
      // Check if this notification was already shown
      final isShown = CacheHelper.getBool(cacheKey) ?? false;
      
      if (isShown) {
        // Check if it's still within the duplicate window
        final expiryTime = CacheHelper.getInt(expiryKey);
        
        if (expiryTime != null) {
          final isExpired = expiryTime < now.millisecondsSinceEpoch;
          
          if (!isExpired) {
            final timeLeft = Duration(milliseconds: expiryTime - now.millisecondsSinceEpoch);
            Logger.w('Background duplicate detected: $messageId (expires in ${timeLeft.inMinutes}m)');
            return false;
          } else {
            // Clean up expired entry
            await _cleanupExpiredEntry(messageId);
            Logger.i('Background notification expired entry cleaned: $messageId');
          }
        } else {
          // Orphaned entry without expiry, clean it up
          await CacheHelper.remove(cacheKey);
          Logger.w('Background notification orphaned entry cleaned: $messageId');
        }
      }
      
      // Record this notification
      await _recordNotification(messageId, now);
      
      Logger.i('Background notification approved: $messageId');
      return true;
      
    } catch (e) {
      Logger.e('Error in background deduplication for $messageId: $e');
      // In case of error, allow the notification to prevent missing important notifications
      return true;
    }
  }
  
  /// Ensure cache is properly initialized with retry mechanism
  static Future<void> _ensureCacheInitialized() async {
    int retries = 0;
    const maxRetries = 3;
    
    while (retries < maxRetries) {
      try {
        await CacheHelper.init();
        
        // Test cache functionality
        const testKey = 'bg_dedup_test';
        await CacheHelper.putString(testKey, 'test');
        final testValue = CacheHelper.getString(testKey);
        await CacheHelper.remove(testKey);
        
        if (testValue == 'test') {
          Logger.i('Background cache initialized successfully');
          return;
        }
        
        throw Exception('Cache test failed');
      } catch (e) {
        retries++;
        Logger.w('Background cache init attempt $retries failed: $e');
        
        if (retries < maxRetries) {
          // Wait before retry with exponential backoff
          await Future.delayed(Duration(milliseconds: 100 * (1 << retries)));
        } else {
          throw Exception('Failed to initialize cache after $maxRetries attempts: $e');
        }
      }
    }
  }
  
  /// Record notification in persistent storage
  static Future<void> _recordNotification(String messageId, DateTime now) async {
    try {
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      final expiryTime = now.add(_duplicateWindow).millisecondsSinceEpoch;
      
      // Use atomic operations to prevent race conditions
      await CacheHelper.putBool(cacheKey, true);
      await CacheHelper.putInt(expiryKey, expiryTime);
      
      Logger.firebase('Background notification recorded: $messageId (expires: ${DateTime.fromMillisecondsSinceEpoch(expiryTime)})');
    } catch (e) {
      Logger.e('Error recording background notification $messageId: $e');
      rethrow;
    }
  }
  
  /// Clean up expired entry
  static Future<void> _cleanupExpiredEntry(String messageId) async {
    try {
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      
      await CacheHelper.remove(cacheKey);
      await CacheHelper.remove(expiryKey);
    } catch (e) {
      Logger.e('Error cleaning up expired entry $messageId: $e');
    }
  }
  
  /// Clean up all expired background notification entries
  static Future<void> cleanupExpiredEntries() async {
    try {
      await _ensureCacheInitialized();
      
      final now = DateTime.now().millisecondsSinceEpoch;
      final keys = CacheHelper.getKeys();
      int cleanedCount = 0;
      
      for (final key in keys) {
        if (key.startsWith(_expiryPrefix)) {
          final expiryTime = CacheHelper.getInt(key);
          if (expiryTime != null && expiryTime < now) {
            final messageId = key.replaceFirst(_expiryPrefix, '');
            await _cleanupExpiredEntry(messageId);
            cleanedCount++;
          }
        }
      }
      
      if (cleanedCount > 0) {
        Logger.i('Background dedup: Cleaned up $cleanedCount expired entries');
      }
    } catch (e) {
      Logger.e('Error cleaning up background notification cache: $e');
    }
  }
  
  /// Get statistics about background notification cache
  static Future<Map<String, dynamic>> getStats() async {
    try {
      await _ensureCacheInitialized();
      
      final keys = CacheHelper.getKeys();
      final notificationKeys = keys.where((key) => key.startsWith(_keyPrefix)).length;
      final expiryKeys = keys.where((key) => key.startsWith(_expiryPrefix)).length;
      
      return {
        'notification_entries': notificationKeys,
        'expiry_entries': expiryKeys,
        'duplicate_window_minutes': _duplicateWindow.inMinutes,
        'cache_prefix': _keyPrefix,
      };
    } catch (e) {
      Logger.e('Error getting background dedup stats: $e');
      return {'error': e.toString()};
    }
  }
  
  /// Clear all background notification cache (for testing/debugging)
  static Future<void> clearAll() async {
    try {
      await _ensureCacheInitialized();
      
      final keys = CacheHelper.getKeys();
      int removedCount = 0;
      
      for (final key in keys) {
        if (key.startsWith(_keyPrefix) || key.startsWith(_expiryPrefix)) {
          await CacheHelper.remove(key);
          removedCount++;
        }
      }
      
      Logger.i('Background dedup: Cleared $removedCount cache entries');
    } catch (e) {
      Logger.e('Error clearing background notification cache: $e');
    }
  }
  
  /// Check if a specific message ID is cached (for debugging)
  static Future<bool> isMessageCached(String messageId) async {
    try {
      await _ensureCacheInitialized();
      
      final cacheKey = '$_keyPrefix$messageId';
      final expiryKey = '$_expiryPrefix$messageId';
      
      final isShown = CacheHelper.getBool(cacheKey) ?? false;
      if (!isShown) return false;
      
      final expiryTime = CacheHelper.getInt(expiryKey);
      if (expiryTime == null) return false;
      
      final now = DateTime.now().millisecondsSinceEpoch;
      return expiryTime > now;
    } catch (e) {
      Logger.e('Error checking if message is cached: $e');
      return false;
    }
  }
}
