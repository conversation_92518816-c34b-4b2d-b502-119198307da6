import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';

/// Unified notification deduplication system that combines persistent storage
/// with in-memory tracking for optimal performance and reliability
class UnifiedNotificationDeduplication {
  static final Map<String, DateTime> _memoryCache = <String, DateTime>{};
  static const int _maxMemoryCacheSize = 50;
  static const Duration _duplicateWindow = Duration(minutes: 10); // Increased window
  static const Duration _persistentWindow = Duration(hours: 24); // Longer persistent window
  
  /// Check if a notification should be shown based on its message ID
  /// Returns true if the notification should be shown, false if it's a duplicate
  static Future<bool> shouldShowNotification(String messageId) async {
    final now = DateTime.now();
    
    // First check memory cache for recent duplicates (fastest)
    if (_isInMemoryCache(messageId, now)) {
      Logger.w('Duplicate notification detected in memory cache: $messageId');
      return false;
    }
    
    // Then check persistent storage for longer-term duplicates
    if (await _isInPersistentCache(messageId, now)) {
      Logger.w('Duplicate notification detected in persistent cache: $messageId');
      return false;
    }
    
    // Record this notification in both caches
    await _recordNotification(messageId, now);
    
    Logger.i('Notification approved for display: $messageId');
    return true;
  }
  
  /// Check if notification exists in memory cache
  static bool _isInMemoryCache(String messageId, DateTime now) {
    if (_memoryCache.containsKey(messageId)) {
      final lastShown = _memoryCache[messageId]!;
      final timeDifference = now.difference(lastShown);
      
      if (timeDifference < _duplicateWindow) {
        return true;
      } else {
        // Remove expired entry from memory
        _memoryCache.remove(messageId);
      }
    }
    return false;
  }
  
  /// Check if notification exists in persistent cache
  static Future<bool> _isInPersistentCache(String messageId, DateTime now) async {
    try {
      final cacheKey = 'unified_notification_$messageId';
      final expiryKey = 'unified_notification_expiry_$messageId';
      
      final isShown = CacheHelper.getBool(cacheKey) ?? false;
      if (!isShown) return false;
      
      final expiryTime = CacheHelper.getInt(expiryKey);
      if (expiryTime == null) {
        // Clean up orphaned entry
        await CacheHelper.remove(cacheKey);
        return false;
      }
      
      final isExpired = expiryTime < now.millisecondsSinceEpoch;
      if (isExpired) {
        // Clean up expired entries
        await CacheHelper.remove(cacheKey);
        await CacheHelper.remove(expiryKey);
        return false;
      }
      
      return true;
    } catch (e) {
      Logger.e('Error checking persistent cache: $e');
      return false;
    }
  }
  
  /// Record notification in both memory and persistent caches
  static Future<void> _recordNotification(String messageId, DateTime now) async {
    try {
      // Record in memory cache
      _memoryCache[messageId] = now;
      
      // Limit memory cache size
      if (_memoryCache.length > _maxMemoryCacheSize) {
        _cleanupMemoryCache();
      }
      
      // Record in persistent cache
      final cacheKey = 'unified_notification_$messageId';
      final expiryKey = 'unified_notification_expiry_$messageId';
      final expiryTime = now.add(_persistentWindow).millisecondsSinceEpoch;
      
      await CacheHelper.putBool(cacheKey, true);
      await CacheHelper.putInt(expiryKey, expiryTime);
      
    } catch (e) {
      Logger.e('Error recording notification: $e');
    }
  }
  
  /// Clean up old entries from memory cache
  static void _cleanupMemoryCache() {
    final now = DateTime.now();
    final cutoffTime = now.subtract(_duplicateWindow);
    
    // Remove expired entries
    _memoryCache.removeWhere((key, timestamp) => timestamp.isBefore(cutoffTime));
    
    // If still too large, remove oldest entries
    if (_memoryCache.length > _maxMemoryCacheSize) {
      final sortedEntries = _memoryCache.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));
      
      final entriesToRemove = _memoryCache.length - _maxMemoryCacheSize;
      for (int i = 0; i < entriesToRemove; i++) {
        _memoryCache.remove(sortedEntries[i].key);
      }
    }
  }
  
  /// Clean up expired entries from persistent storage
  static Future<void> cleanupExpiredEntries() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final keys = CacheHelper.getKeys();
      int cleanedCount = 0;
      
      for (final key in keys) {
        if (key.startsWith('unified_notification_expiry_')) {
          final expiryTime = CacheHelper.getInt(key);
          if (expiryTime != null && expiryTime < now) {
            final messageId = key.replaceFirst('unified_notification_expiry_', '');
            await CacheHelper.remove('unified_notification_$messageId');
            await CacheHelper.remove(key);
            cleanedCount++;
          }
        }
      }
      
      if (cleanedCount > 0) {
        Logger.i('Cleaned up $cleanedCount expired unified notification entries');
      }
    } catch (e) {
      Logger.e('Error cleaning up unified notification cache: $e');
    }
  }
  
  /// Clear all notification history (useful for testing or reset)
  static Future<void> clearAllHistory() async {
    try {
      _memoryCache.clear();
      
      final keys = CacheHelper.getKeys();
      for (final key in keys) {
        if (key.startsWith('unified_notification_')) {
          await CacheHelper.remove(key);
        }
      }
      
      Logger.i('All unified notification history cleared');
    } catch (e) {
      Logger.e('Error clearing unified notification history: $e');
    }
  }
  
  /// Get current cache statistics (useful for debugging)
  static Map<String, dynamic> getCacheStats() {
    final keys = CacheHelper.getKeys();
    final persistentCount = keys.where((key) => key.startsWith('unified_notification_')).length ~/ 2;
    
    return {
      'memoryCount': _memoryCache.length,
      'persistentCount': persistentCount,
      'maxMemorySize': _maxMemoryCacheSize,
      'duplicateWindowMinutes': _duplicateWindow.inMinutes,
      'persistentWindowHours': _persistentWindow.inHours,
    };
  }
}
