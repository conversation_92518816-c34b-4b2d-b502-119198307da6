import 'dart:convert';
import 'package:crypto/crypto.dart';
import '../models/notification_data.dart';
import 'logger.dart';

/// Generates consistent content-based IDs for notifications
/// This ensures identical content produces identical IDs regardless of Firebase message ID
class ContentHasher {
  /// Generate a content-based ID for notification deduplication
  /// This is the SINGLE source of truth for notification ID generation
  static String generateNotificationId(NotificationData notification) {
    try {
      // Get stable content for hashing
      final content = notification.contentForHashing;

      if (content.isEmpty) {
        // Fallback for empty content
        final fallbackContent =
            'empty_notification_${DateTime.now().millisecondsSinceEpoch}';
        final fallbackId = _hashContent(fallbackContent);

        NotificationLogger.warning(
            'Generated fallback ID for empty notification: $fallbackId');
        return fallbackId;
      }

      // Generate hash-based ID
      final hashedId = _hashContent(content);

      NotificationLogger.debug(
          'Generated content-based ID: $hashedId for notification: "${notification.title}" '
          '(Firebase ID: ${notification.firebaseMessageId})');

      // Add detailed content logging for debugging
      NotificationLogger.debug(
          'Content for hashing: "$content" | Data: ${notification.data}');

      return hashedId;
    } catch (e) {
      // Fallback to timestamp-based ID on error
      final fallbackId = 'error_${DateTime.now().millisecondsSinceEpoch}';

      NotificationLogger.error(
          'Error generating notification ID: $e. Using fallback: $fallbackId');
      return fallbackId;
    }
  }

  /// Generate hash from content string
  static String _hashContent(String content) {
    // Use SHA-256 for consistent, collision-resistant hashing
    final bytes = utf8.encode(content);
    final digest = sha256.convert(bytes);

    // Convert to shorter, more manageable ID (first 12 characters)
    // This gives us 16^12 = ~281 trillion possible values, very low collision chance
    return digest.toString().substring(0, 12);
  }

  /// Validate that two notifications have the same content
  static bool haveSameContent(
      NotificationData notification1, NotificationData notification2) {
    try {
      final content1 = notification1.contentForHashing;
      final content2 = notification2.contentForHashing;

      return content1 == content2;
    } catch (e) {
      NotificationLogger.error('Error comparing notification content: $e');
      return false;
    }
  }

  /// Generate a quick hash for rapid comparison
  static int generateQuickHash(NotificationData notification) {
    try {
      final content = notification.contentForHashing;
      return content.hashCode.abs();
    } catch (e) {
      NotificationLogger.error('Error generating quick hash: $e');
      return DateTime.now().millisecondsSinceEpoch.hashCode.abs();
    }
  }

  /// Extract stable data from notification data map
  /// Removes fields that commonly change between identical notifications
  static Map<String, dynamic> extractStableData(Map<String, dynamic> data) {
    if (data.isEmpty) return {};

    final stableData = Map<String, dynamic>.from(data);

    // Remove timestamp-based and ID fields that change between identical notifications
    final unstableKeys = <String>[];

    for (final key in stableData.keys) {
      final lowerKey = key.toLowerCase();
      if (lowerKey.contains('timestamp') ||
          lowerKey.contains('time') ||
          lowerKey.contains('id') ||
          lowerKey.contains('uuid') ||
          lowerKey.contains('sent') ||
          lowerKey.contains('created') ||
          lowerKey.contains('received') ||
          lowerKey.contains('generated') ||
          lowerKey.contains('sequence') ||
          lowerKey.contains('count')) {
        unstableKeys.add(key);
      }
    }

    // Remove unstable keys
    for (final key in unstableKeys) {
      stableData.remove(key);
    }

    return stableData;
  }

  /// Create a content signature for debugging
  static String createContentSignature(NotificationData notification) {
    try {
      final content = notification.contentForHashing;
      final quickHash = generateQuickHash(notification);
      final fullId = generateNotificationId(notification);

      return 'Content: "${content.length > 50 ? content.substring(0, 50) + "..." : content}" | '
          'QuickHash: $quickHash | FullID: $fullId';
    } catch (e) {
      return 'Error creating signature: $e';
    }
  }

  /// Validate content hashing consistency
  static bool validateHashingConsistency(NotificationData notification,
      {int iterations = 10}) {
    try {
      final firstId = generateNotificationId(notification);

      for (int i = 0; i < iterations; i++) {
        final currentId = generateNotificationId(notification);
        if (currentId != firstId) {
          NotificationLogger.error(
              'Hash inconsistency detected! First: $firstId, Current: $currentId');
          return false;
        }
      }

      NotificationLogger.debug(
          'Hash consistency validated for notification: $firstId');
      return true;
    } catch (e) {
      NotificationLogger.error('Error validating hash consistency: $e');
      return false;
    }
  }
}
