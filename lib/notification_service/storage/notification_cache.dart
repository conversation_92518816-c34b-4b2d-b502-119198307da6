import 'package:busaty_parents/helper/cache_helper.dart';
import '../models/notification_config.dart';
import '../utils/logger.dart';

/// Persistent storage for notification deduplication
/// Survives app restarts and works across all app states
class NotificationCache {
  static bool _isInitialized = false;

  /// Initialize the cache system with robust error handling
  static Future<void> initialize() async {
    if (_isInitialized) return;

    int retries = 0;
    const maxRetries = 5;

    while (retries < maxRetries) {
      try {
        await CacheHelper.init();

        // Test cache functionality
        const testKey = '${NotificationConfig.notificationCachePrefix}test';
        const testValue = 'test_value';

        await CacheHelper.putString(testKey, testValue);
        final retrievedValue = CacheHelper.getString(testKey);
        await CacheHelper.remove(testKey);

        if (retrievedValue == testValue) {
          _isInitialized = true;

          if (retries > 0) {
            NotificationLogger.info(
                'Cache initialized successfully after $retries retries');
          } else {
            NotificationLogger.debug('Cache initialized successfully');
          }

          // Schedule initial cleanup
          _scheduleCleanup();
          return;
        }

        throw Exception(
            'Cache test failed - retrieved: $retrievedValue, expected: $testValue');
      } catch (e) {
        retries++;
        NotificationLogger.warning(
            'Cache init attempt $retries/$maxRetries failed: $e');

        if (retries < maxRetries) {
          final delayMs = 50 * (1 << (retries - 1)); // Exponential backoff
          await Future.delayed(Duration(milliseconds: delayMs));
        } else {
          throw Exception(
              'Failed to initialize cache after $maxRetries attempts: $e');
        }
      }
    }
  }

  /// Check if a notification should be shown (not a duplicate)
  static Future<bool> shouldShowNotification(String notificationId) async {
    try {
      await _ensureInitialized();

      final now = DateTime.now();
      final cacheKey =
          '${NotificationConfig.notificationCachePrefix}$notificationId';
      final expiryKey = '${NotificationConfig.expiryPrefix}$notificationId';
      final lastShownKey =
          '${NotificationConfig.lastShownPrefix}$notificationId';

      NotificationLogger.debug('Checking deduplication for: $notificationId');

      // Check main deduplication window
      final isShown = CacheHelper.getBool(cacheKey) ?? false;

      if (isShown) {
        final expiryTime = CacheHelper.getInt(expiryKey);

        if (expiryTime != null) {
          final isExpired = expiryTime < now.millisecondsSinceEpoch;

          if (!isExpired) {
            final timeLeft =
                Duration(milliseconds: expiryTime - now.millisecondsSinceEpoch);
            NotificationLogger.warning(
                'DUPLICATE BLOCKED: $notificationId (expires in ${timeLeft.inMinutes}m ${timeLeft.inSeconds % 60}s)');
            return false;
          } else {
            // Clean up expired entry
            await _cleanupEntry(notificationId);
            NotificationLogger.debug(
                'Expired entry cleaned for: $notificationId');
          }
        } else {
          // Orphaned entry, clean it up
          await CacheHelper.remove(cacheKey);
          NotificationLogger.warning(
              'Orphaned entry cleaned for: $notificationId');
        }
      }

      // Check rapid-fire duplicate protection
      final lastShownTime = CacheHelper.getInt(lastShownKey);
      if (lastShownTime != null) {
        final timeSinceLastShown = now.millisecondsSinceEpoch - lastShownTime;
        if (timeSinceLastShown <
            NotificationConfig.rapidFireWindow.inMilliseconds) {
          NotificationLogger.warning(
              'RAPID-FIRE DUPLICATE BLOCKED: $notificationId (shown ${timeSinceLastShown}ms ago)');
          return false;
        }
      }

      // Record this notification
      await _recordNotification(notificationId, now);

      NotificationLogger.info('NOTIFICATION APPROVED: $notificationId');
      return true;
    } catch (e) {
      NotificationLogger.error(
          'Error in deduplication check for $notificationId: $e');

      // Fail-safe mode: allow notification to prevent missing important messages
      if (NotificationConfig.failSafeMode) {
        NotificationLogger.warning(
            'Allowing notification due to error (fail-safe mode)');
        return true;
      }

      return false;
    }
  }

  /// Record a notification in the cache
  static Future<void> _recordNotification(
      String notificationId, DateTime now) async {
    try {
      final cacheKey =
          '${NotificationConfig.notificationCachePrefix}$notificationId';
      final expiryKey = '${NotificationConfig.expiryPrefix}$notificationId';
      final lastShownKey =
          '${NotificationConfig.lastShownPrefix}$notificationId';
      final expiryTime =
          NotificationConfig.getCacheExpiryTime(now).millisecondsSinceEpoch;

      // Atomic operations to prevent race conditions
      await CacheHelper.putBool(cacheKey, true);
      await CacheHelper.putInt(expiryKey, expiryTime);
      await CacheHelper.putInt(lastShownKey, now.millisecondsSinceEpoch);

      final expiryDateTime = DateTime.fromMillisecondsSinceEpoch(expiryTime);
      NotificationLogger.debug(
          'Notification recorded: $notificationId (expires: ${expiryDateTime.toString().substring(11, 19)})');
    } catch (e) {
      NotificationLogger.error(
          'Error recording notification $notificationId: $e');
      rethrow;
    }
  }

  /// Clean up a specific notification entry
  static Future<void> _cleanupEntry(String notificationId) async {
    try {
      final cacheKey =
          '${NotificationConfig.notificationCachePrefix}$notificationId';
      final expiryKey = '${NotificationConfig.expiryPrefix}$notificationId';
      final lastShownKey =
          '${NotificationConfig.lastShownPrefix}$notificationId';

      await CacheHelper.remove(cacheKey);
      await CacheHelper.remove(expiryKey);
      await CacheHelper.remove(lastShownKey);
    } catch (e) {
      NotificationLogger.error('Error cleaning up entry $notificationId: $e');
    }
  }

  /// Clean up all expired entries
  static Future<void> cleanupExpiredEntries() async {
    try {
      await _ensureInitialized();

      final now = DateTime.now().millisecondsSinceEpoch;
      final keys = CacheHelper.getKeys();
      int cleanedCount = 0;

      for (final key in keys) {
        if (key.startsWith(NotificationConfig.expiryPrefix)) {
          final expiryTime = CacheHelper.getInt(key);
          if (expiryTime != null && expiryTime < now) {
            final notificationId =
                key.replaceFirst(NotificationConfig.expiryPrefix, '');
            await _cleanupEntry(notificationId);
            cleanedCount++;
          }
        }
      }

      if (cleanedCount > 0) {
        NotificationLogger.info(
            'Cleaned up $cleanedCount expired notification entries');
      }

      // Update last cleanup time
      await CacheHelper.putInt(
          '${NotificationConfig.cleanupPrefix}last_cleanup', now);
    } catch (e) {
      NotificationLogger.error('Error cleaning up expired entries: $e');
    }
  }

  /// Get cache statistics
  static Future<Map<String, dynamic>> getStats() async {
    try {
      await _ensureInitialized();

      final keys = CacheHelper.getKeys();
      final notificationKeys = keys
          .where((key) =>
              key.startsWith(NotificationConfig.notificationCachePrefix))
          .length;
      final expiryKeys = keys
          .where((key) => key.startsWith(NotificationConfig.expiryPrefix))
          .length;

      final now = DateTime.now().millisecondsSinceEpoch;
      int activeCount = 0;
      int expiredCount = 0;

      for (final key in keys) {
        if (key.startsWith(NotificationConfig.expiryPrefix)) {
          final expiryTime = CacheHelper.getInt(key);
          if (expiryTime != null) {
            if (expiryTime > now) {
              activeCount++;
            } else {
              expiredCount++;
            }
          }
        }
      }

      return {
        'total_notification_entries': notificationKeys,
        'total_expiry_entries': expiryKeys,
        'active_entries': activeCount,
        'expired_entries': expiredCount,
        'deduplication_window_minutes':
            NotificationConfig.deduplicationWindow.inMinutes,
        'rapid_fire_window_seconds':
            NotificationConfig.rapidFireWindow.inSeconds,
        'cache_initialized': _isInitialized,
        'system_type': 'rebuilt_notification_system',
      };
    } catch (e) {
      NotificationLogger.error('Error getting cache stats: $e');
      return {'error': e.toString()};
    }
  }

  /// Clear all cache entries (for testing/debugging)
  static Future<void> clearAll() async {
    try {
      await _ensureInitialized();

      final keys = CacheHelper.getKeys();
      int removedCount = 0;

      for (final key in keys) {
        if (key.startsWith(NotificationConfig.notificationCachePrefix) ||
            key.startsWith(NotificationConfig.expiryPrefix) ||
            key.startsWith(NotificationConfig.lastShownPrefix) ||
            key.startsWith(NotificationConfig.cleanupPrefix)) {
          await CacheHelper.remove(key);
          removedCount++;
        }
      }

      NotificationLogger.info('Cleared $removedCount cache entries');
    } catch (e) {
      NotificationLogger.error('Error clearing cache: $e');
    }
  }

  /// Ensure cache is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Schedule periodic cleanup
  static void _scheduleCleanup() {
    // Run cleanup every hour
    Future.delayed(NotificationConfig.cleanupInterval, () async {
      await cleanupExpiredEntries();
      _scheduleCleanup(); // Schedule next cleanup
    });
  }
}
