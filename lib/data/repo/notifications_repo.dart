import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/data/models/track_data_model/trip_data_model.dart';

import '../../config/config_base.dart';
import '../../config/global_variable.dart';
import '../../helper/network_serviecs.dart';
import '../models/notifications_models/all_notifications_model.dart';
import '../models/notifications_models/fcm_response.dart';
import '../models/notifications_models/fcm_token_model.dart';
import '../models/notifications_models/show_notification_model.dart';
import '../models/notifications_models/store_notification_model.dart';

class NotificationsRepo {
  final _dio = NetworkService();

  Future<FCMResponse> sendNotification(
      {required List<String> deviceTokens, String? title, String? body}) async {
    try {
      Dio dio = Dio();

      Map<String, dynamic> requestBody = {
        'registration_ids': deviceTokens,
        'priority': 'high', // FCM message priority
        'notification': {
          'body': '$body',
          'title': '$title',
          'sound': 'default',
          'click_action': 'FLUTTER_NOTIFICATION_CLICK',
        },
        'data': {
          'priority': 'high',
          'click_action': 'FLUTTER_NOTIFICATION_CLICK',
          'urls': '',
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        },
        'android': {
          'priority': 'high',
          'notification': {
            'priority': 'high',
            'default_sound': true,
            'default_vibrate_timings': true,
            'default_light_settings': true,
          }
        },
        'apns': {
          'headers': {
            'apns-priority': '10', // Highest priority for iOS
            'apns-push-type': 'alert',
          },
          'payload': {
            'aps': {
              'alert': {
                'title': '$title',
                'body': '$body',
              },
              'badge': 1,
              'sound': 'default',
              'content-available': 1,
            }
          }
        }
      };

      Options options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=$firebaseServerKey',
        },
      );

      Response response = await dio.post(
        ConfigBase.fcmUrl,
        data: requestBody,
        options: options,
      );

      FCMResponse? notificationsModel;
      if (response.statusCode == 200) {
        notificationsModel = FCMResponse.fromMap(response.data);
        debugPrint('Notification sent successfully');
        debugPrint('${response.data}');
      } else {
        notificationsModel = FCMResponse.fromMap(response.data);
        debugPrint('Failed to send notification: ${response.statusCode}');
        debugPrint(response.data);
      }
      return notificationsModel;
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint('Error sending notification: $e');
      return FCMResponse();
    }
  }

  Future<FcmTokenModel> schoolFcmToken({String? schoolId}) async {
    try {
      final request = await _dio.get(
        url: '${ConfigBase.schoolFcmTokensS}/$schoolId',
        isAuth: true,
      );
      // Logger().w(request.data);
      FcmTokenModel? fcmTokenModel;
      if (request.statusCode == 200) {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      } else {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      }
      return fcmTokenModel;
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("catch error $e");
      return FcmTokenModel(message: e.toString());
    }
  }

  Future<FcmTokenModel> supervisorFcmToken({String? childId}) async {
    try {
      final request = await _dio.get(
        url: '${ConfigBase.supervisorsFcmTokens}/$childId',
        isAuth: true,
      );
      FcmTokenModel? fcmTokenModel;
      if (request.statusCode == 200) {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      } else {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      }
      return fcmTokenModel;
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("catch error $e");
      return FcmTokenModel(message: e.toString());
    }
  }

  Future<StoreNotificationModel> storeSchoolNotification(
      {int? schoolId, String? title, body, route}) async {
    // Logger().w("this is schools id $schoolId ");
    try {
      final request = await _dio.post(
        url: '${ConfigBase.storeSchoolNotification}/$schoolId',
        body: {"title": title, "body": body, "route": route},
        isAuth: true,
      );
      // Logger().w("this is notification ${request.data}");
      StoreNotificationModel? storeSchoolNotification;
      if (request.statusCode == 200) {
        debugPrint('storeSchoolNotification Success');
        storeSchoolNotification = StoreNotificationModel.fromMap(request.data);
      } else {
        debugPrint('storeSchoolNotification Failed');
        storeSchoolNotification = StoreNotificationModel.fromMap(request.data);
      }
      return storeSchoolNotification;
    } on Exception catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("error $e");
      return StoreNotificationModel(message: e.toString());
    }
  }

  // Future<StoreNotificationModel> storeBusNotification(
  //     {int? busId, String? title, body, route}) async {
  //   try {
  //     final request = await _dio.post(
  //       url: '${ConfigBase.storeBusNotification}/$busId',
  //       body: {"title": title, "body": body, "route": route},
  //       isAuth: true,
  //     );
  //     StoreNotificationModel? storeBusNotification;
  //     if (request.statusCode == 200) {
  //       debugPrint('storeBusNotification Success');
  //       storeBusNotification = StoreNotificationModel.fromMap(request.data);
  //     } else {
  //       debugPrint('storeBusNotification Failed');
  //       storeBusNotification = StoreNotificationModel.fromMap(request.data);
  //     }
  //     return storeBusNotification;
  //   } on Exception catch (e, stackTrace) {
  //     debugPrint("catch error at Absence repo: $e");
  //     debugPrint(stackTrace.toString());
  //     debugPrint("error $e");
  //     return StoreNotificationModel(message: e.toString());
  //   }
  // }
  Future<StoreNotificationModel> storeBusNotification({
    required int? busId,
    required String? title,
    required String? body,
    required String? route,
  }) async {
    try {
      // Input validation
      if (busId == null || title == null || body == null || route == null) {
        return StoreNotificationModel(message: 'Missing required parameters');
      }

      final request = await _dio.post(
        url: '${ConfigBase.storeBusNotification}/$busId',
        body: {
          "title": title,
          "body": body,
          "route": route,
        },
        isAuth: true,
      );

      // Log response status
      if (request.statusCode == 200) {
        debugPrint('storeBusNotification Success');
      } else {
        debugPrint(
            'storeBusNotification Failed with status: ${request.statusCode}');
      }

      // Parse response data
      return StoreNotificationModel.fromMap(request.data);
    } on DioException catch (e, stackTrace) {
      debugPrint("DioError in storeBusNotification: ${e.message}");
      debugPrint("Status code: ${e.response?.statusCode}");
      debugPrint(stackTrace.toString());
      return StoreNotificationModel(message: e.message);
    } catch (e, stackTrace) {
      debugPrint("Error in storeBusNotification: $e");
      debugPrint(stackTrace.toString());
      return StoreNotificationModel(message: e.toString());
    }
  }

  Future<TrackDataModel> trackTrip({required int id}) async {
    try {
      final request = await _dio.get(
        // https://stage.busatyapp.com/api/parents/trips/183
        url: 'https://stage.busatyapp.com/api/parents/trips/$id',

        // "${ConfigBase.allNotifications}",
        isAuth: true,
      );
      // Logger().w(request.data);
      TrackDataModel? trackDataModel;
      if (request.statusCode == 200) {
        trackDataModel = TrackDataModel.fromJson(request.data);
      } else {
        trackDataModel = TrackDataModel.fromJson(request.data);
      }
      return trackDataModel;
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("catch error $e");
      return TrackDataModel();
    }
  }

  Future<AllNotificationsModel1> allNotifications({int? page}) async {
    try {
      final request = await _dio.get(
        // https://stage.busatyapp.com/api/parents/trips/183
        url: '${ConfigBase.allNotifications}?page=$page&limit=10',
        queryParameters: {"read_at": 1},

        // "${ConfigBase.allNotifications}",
        isAuth: true,
      );
      // Logger().w(request.data);
      AllNotificationsModel1? allNotificationsModel;
      if (request.statusCode == 200) {
        allNotificationsModel = AllNotificationsModel1.fromJson(request.data);
      } else {
        allNotificationsModel = AllNotificationsModel1.fromJson(request.data);
      }
      return allNotificationsModel;
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("catch error $e");
      return AllNotificationsModel1();
    }
  }

  Future<AllNotificationsModel1> unreadNotifications({int? page}) async {
    try {
      final request = await _dio.get(
          url: "${ConfigBase.allNotifications}?page=$page&limit=10",
          isAuth: true,
          queryParameters: {"read_at": 0});
      AllNotificationsModel1? unreadNotificationsModel;
      // Logger().e(request.data);
      if (request.statusCode == 200) {
        unreadNotificationsModel =
            AllNotificationsModel1.fromJson(request.data);
        // Logger().e(unreadNotificationsModel);
      } else {
        unreadNotificationsModel =
            AllNotificationsModel1.fromJson(request.data);
      }
      return unreadNotificationsModel;
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("catch error $e");
      rethrow;
    }
  }

  Future<ShowNotificationModel> showNotification({int? id}) async {
    try {
      final request = await _dio.get(
        //https://stage.busatyapp.com/attendants/messages/show/id
        url: "${ConfigBase.showNotification}/$id",
        isAuth: true,
      );
      Logger.w(request.data);
      ShowNotificationModel? showNotificationModel;
      if (request.statusCode == 200) {
        showNotificationModel = ShowNotificationModel.fromJson(request.data);
      } else {
        showNotificationModel = ShowNotificationModel.fromJson(request.data);
      }
      return showNotificationModel;
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("catch error $e");
      return ShowNotificationModel(message: e.toString());
    }
  }
}
