// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'package:busaty_parents/config/config_base.dart';
import 'package:busaty_parents/config/global_variable.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';

import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/utils/assets_utils.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_button.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/widgets/custom_background_image.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../bloc/cubit/forgot_password_cubit/forgot_password_cubit.dart';
import '../../custom_widgets/custom_form_field_border.dart';
import '../home_screen/home_screen.dart';

/// A screen widget that handles verification code input and validation.
/// It is used for both email verification and password reset flows.
class SendCodeScreen extends StatefulWidget {
  static const String routeName = PathRouteName.sendCode;
  final bool isForgotPassword;
  final String? email;

  const SendCodeScreen({
    super.key,
    this.isForgotPassword = false,
    this.email,
  });

  @override
  State<SendCodeScreen> createState() => _SendCodeScreenState();
}

class _SendCodeScreenState extends State<SendCodeScreen> {
  String? smsCode;
  String? password;
  String? confirmedPassword;
  bool securityCheck = true;
  bool securityCheck1 = true;
  final _formKey = GlobalKey<FormState>();
  int seconds = 120;

  @override
  void initState() {
    super.initState();
    _initializeAndValidate();
    _startCountdown();
  }

  /// Validates the FCM token (no longer initializes FCM as it's done in main.dart).
  Future<void> _initializeAndValidate() async {
    // FCM is already initialized in main.dart, just validate the token
    if (fCMToken == null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Error: Failed to initialize notifications. Please check your internet connection and try again.',
          ),
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  /// Starts a countdown timer for resending the code.
  void _startCountdown() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) return;
      setState(() {
        if (seconds > 0) {
          seconds--;
        } else {
          seconds = 0;
          timer.cancel();
        }
      });
    });
  }

  /// Makes a POST request to the given [endpoint] with [data].
  Future<Response> _postRequest(
      String endpoint, Map<String, dynamic> data) async {
    final authHeaders = {'Authorization': "Bearer $tempToken"};
    final dio = Dio();

    try {
      // Log request details
      debugPrint('┌─────────── API Request ───────────');
      debugPrint('│ Endpoint: $endpoint');
      debugPrint('│ Data: $data');
      debugPrint('│ Headers: $authHeaders');
      debugPrint('└────────────────────────────────────');

      final response = await dio.post(
        "${ConfigBase.baseUrl}$endpoint",
        data: data,
        options: Options(
          headers: authHeaders,
          validateStatus: (status) =>
              true, // Accept any status code to handle it manually
        ),
      );

      // Log response details
      debugPrint('┌─────────── API Response ──────────');
      debugPrint('│ Status Code: ${response.statusCode}');
      debugPrint('│ Response Data: ${response.data}');
      debugPrint('└────────────────────────────────────');

      // Handle error status codes
      if (response.statusCode == 500) {
        throw DioException(
            requestOptions: response.requestOptions,
            response: response,
            error:
                'Server Error: ${response.data['message'] ?? 'Unknown server error'}');
      }

      return response;
    } catch (e) {
      debugPrint('┌─────────── API Error ───────────');
      if (e is DioException) {
        debugPrint('│ Type: ${e.type}');
        debugPrint('│ Message: ${e.message}');
        if (e.response != null) {
          debugPrint('│ Status: ${e.response?.statusCode}');
          debugPrint('│ Data: ${e.response?.data}');
        }
      } else {
        debugPrint('│ Error: $e');
      }
      debugPrint('└────────────────────────────────────');
      rethrow;
    }
  }

  /// Controller for the PIN code field
  TextEditingController pinController = TextEditingController();

  /// Handles the resend code functionality.
  Future<void> _handleResend() async {
    setState(() {
      seconds = 60;
      // Clear the existing PIN code
      smsCode = null;
      pinController.clear();
    });

    try {
      final resendResponse = await _postRequest(
        "resendCode",
        {
          "firebase_token": fCMToken,
        },
      );

      if (!mounted) return;

      if (resendResponse.statusCode == 200) {
        String successMessage = context.locale.toString() == "ar"
            ? 'تم إرسال الكود مرة أخرى بنجاح'
            : 'Code sent again successfully';
        if (resendResponse.data is Map &&
            resendResponse.data['message'] != null) {
          successMessage = resendResponse.data['message'];
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.greenSuccess,
            content: CustomText(
              text: successMessage,
              color: TColor.white,
              maxLine: 2,
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        String errorMessage = context.locale.toString() == "ar"
            ? "حدث خطأ أثناء إرسال الكود"
            : "Error occurred while sending code";
        if (resendResponse.data is Map &&
            resendResponse.data['message'] != null) {
          errorMessage = resendResponse.data['message'];
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.redAccent,
            content: CustomText(
              text: errorMessage,
              color: TColor.white,
              maxLine: 2,
            ),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      String errorMessage = context.locale.toString() == "ar"
          ? "حدث خطأ في الاتصال"
          : "Connection error occurred";
      if (e is DioException &&
          e.response?.data is Map &&
          e.response?.data['message'] != null) {
        errorMessage = e.response!.data['message'];
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: errorMessage,
            color: TColor.white,
            maxLine: 2,
          ),
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// Handles verification of the code when user taps the Next button.
  Future<void> _handleVerification() async {
    if (smsCode == null || smsCode!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: '${AppStrings.code.tr()} ${AppStrings.isRequired.tr()}',
            color: TColor.white,
          ),
        ),
      );
      return;
    }

    try {
      final verifyResponse = await _postRequest(
        "verify",
        {
          "token": smsCode,
          "firebase_token": fCMToken,
        },
      );

      if (verifyResponse.statusCode == 200) {
        await CacheHelper.putString("token", tempToken!);
        token = tempToken;

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.greenSuccess,
            content: CustomText(
              text: AppStrings.emailVerified.tr(),
              color: TColor.white,
            ),
          ),
        );
        Navigator.popAndPushNamed(context, HomeScreen.routeName);
      } else {
        if (!mounted) return;

        String errorMessage = AppStrings.wrongCode.tr();
        if (verifyResponse.data is Map &&
            verifyResponse.data['message'] != null) {
          errorMessage = verifyResponse.data['message'];
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.redAccent,
            content: CustomText(
              text: errorMessage,
              color: TColor.white,
            ),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      String errorMessage = AppStrings.wrongCode.tr();
      if (e is DioException &&
          e.response?.data is Map &&
          e.response?.data['message'] != null) {
        errorMessage = e.response!.data['message'];
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: errorMessage,
            color: TColor.white,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("#####tempToken: $tempToken");
    debugPrint("#####token: $token");

    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const Sbox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const Sbox(h: 60),
              CustomText(
                text: AppStrings.sendCode.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const Sbox(h: 20),
              Container(
                width: 354.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Padding(
                        padding:
                            EdgeInsets.only(top: 15.h, left: 15.w, right: 15.w),
                        child: Row(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: TColor.redAccent.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20.r),
                                border: Border.all(
                                  color:
                                      TColor.redAccent.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: TextButton.icon(
                                onPressed: () => Navigator.pop(context),
                                icon: Icon(
                                  Icons.edit_outlined,
                                  color: TColor.redAccent,
                                  size: 16.sp,
                                ),
                                label: CustomText(
                                  text: context.locale.toString() == "ar"
                                      ? "بريد إلكتروني خاطئ؟"
                                      : "Wrong Email?",
                                  color: TColor.redAccent,
                                  fontW: FontWeight.w600,
                                  fontSize: 13,
                                ),
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 12.w, vertical: 8.h),
                                  minimumSize: Size.zero,
                                  tapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                ),
                              ),
                            ),
                            const Spacer(),
                          ],
                        ),
                      ),
                      const Sbox(h: 5),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: CustomText(
                          text: context.locale.toString() == "ar"
                              ? 'تم إرسال الكود إلى ${widget.email ?? ''}'
                              : 'Code sent to ${widget.email ?? ''}',
                          color: TColor.backgroundContainer,
                          fontW: FontWeight.w600,
                          fontSize: 15,
                          textAlign: TextAlign.center,
                          maxLine: 2,
                        ),
                      ),
                      if (widget.isForgotPassword) ...[
                        const Sbox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          contentPaddingVertical: 0,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          hintText: AppStrings.password.tr(),
                          requiredNumber: 6,
                          saved: (value) => password = value,
                          validation: AppStrings.validPassword.tr(),
                          security: securityCheck,
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck = !securityCheck;
                              });
                            },
                            child: Icon(
                              securityCheck
                                  ? Icons.visibility_off
                                  : Icons.visibility_outlined,
                              color: TColor.iconInputColor,
                            ),
                          ),
                        ),
                        const Sbox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          contentPaddingVertical: 0,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          requiredNumber: 6,
                          hintText: AppStrings.confirmPassword.tr(),
                          saved: (value) => confirmedPassword = value,
                          validation: AppStrings.validConfirmPassword.tr(),
                          security: securityCheck1,
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck1 = !securityCheck1;
                              });
                            },
                            child: Icon(
                              securityCheck1
                                  ? Icons.visibility_off
                                  : Icons.visibility_outlined,
                              color: TColor.iconInputColor,
                            ),
                          ),
                        ),
                      ],
                      const Sbox(h: 20),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Column(
                          children: [
                            CustomText(
                              text: context.locale.toString() == "ar"
                                  ? 'أدخل الكود المرسل'
                                  : 'Enter the sent code',
                              color: TColor.backgroundContainer,
                              fontW: FontWeight.w600,
                              fontSize: 14,
                            ),
                            const Sbox(h: 15),
                            PinCodeTextField(
                              appContext: context,
                              autoFocus: true,
                              controller: pinController,
                              cursorColor: TColor.mainColor,
                              keyboardType: TextInputType.number,
                              length: 6,
                              obscureText: false,
                              animationType: AnimationType.scale,
                              pinTheme: PinTheme(
                                shape: PinCodeFieldShape.box,
                                borderRadius: BorderRadius.circular(12.r),
                                fieldHeight: 55.w,
                                fieldWidth: 45.w,
                                borderWidth: 2,
                                activeColor: TColor.mainColor,
                                inactiveColor: TColor.fillFormFieldB,
                                inactiveFillColor: TColor.fillFormFieldB,
                                activeFillColor: Colors.white,
                                selectedColor: TColor.mainColor,
                                selectedFillColor:
                                    TColor.mainColor.withValues(alpha: 0.1),
                                errorBorderColor: TColor.redAccent,
                              ),
                              animationDuration:
                                  const Duration(milliseconds: 300),
                              backgroundColor: Colors.transparent,
                              enableActiveFill: true,
                              onCompleted: (submitCode) {
                                smsCode = submitCode;
                                debugPrint("PIN completed: $submitCode");
                              },
                              onChanged: (value) {
                                smsCode = value;
                                debugPrint("PIN changed: $value");
                              },
                              beforeTextPaste: (text) {
                                debugPrint("Pasting text: $text");
                                return true;
                              },
                            ),
                          ],
                        ),
                      ),
                      const Sbox(h: 10),
                      seconds != 0
                          ? Center(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.w, vertical: 8.h),
                                decoration: BoxDecoration(
                                  color:
                                      TColor.mainColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(20.r),
                                  border: Border.all(
                                    color:
                                        TColor.mainColor.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: CustomText(
                                  text: context.locale.toString() == "ar"
                                      ? 'إعادة إرسال الكود بعد $secondsث'
                                      : 'Resend Code After ${seconds}s',
                                  color: TColor.mainColor,
                                  fontW: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                            )
                          : CustomButton(
                              text: context.locale.toString() == "ar"
                                  ? "إعادة الإرسال"
                                  : "Resend",
                              onTap: _handleResend,
                              width: 140,
                              height: 48,
                              radius: 15,
                              borderColor: TColor.mainColor,
                              bgColor: TColor.mainColor,
                            ),
                      const Sbox(h: 40),
                      BlocConsumer<ForgotPasswordCubit, ForgotPasswordState>(
                        listener: (context, state) {
                          if (state is ForgotPasswordSuccess) {
                            Navigator.of(context)
                                .popUntil((route) => route.isFirst);
                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                  builder: (context) => const HomeScreen()),
                            );
                          }
                        },
                        builder: (context, state) {
                          if (state is ForgotPasswordLoading) {
                            return const CircularProgressIndicator(
                              color: TColor.mainColor,
                            );
                          } else {
                            return CustomButton(
                              text: AppStrings.next.tr(),
                              onTap: () async {
                                if (widget.isForgotPassword) {
                                  if (_formKey.currentState!.validate()) {
                                    _formKey.currentState!.save();
                                    ForgotPasswordCubit.get(context)
                                        .resetPassword(
                                      email: widget.email,
                                      code: smsCode,
                                      password: password,
                                      passwordConfirmation: confirmedPassword,
                                    );
                                  }
                                } else {
                                  await _handleVerification();
                                }
                              },
                              width: 307,
                              height: 48,
                              radius: 15,
                              borderColor: TColor.mainColor,
                              bgColor: TColor.mainColor,
                            );
                          }
                        },
                      ),
                      const Sbox(h: 30),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
