import 'package:busaty_parents/helper/custom_date_time.dart';

import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_button.dart';
import 'package:busaty_parents/widgets/row_details_widget.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import '../../../bloc/cubit/notifications_cubit/notifications_cubit.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../custom_widgets/custom_text.dart';

class NotificationsScreen extends StatelessWidget {
  NotificationsScreen({
    super.key,
  });

  static const routeName = PathRouteName.notificationsScreen;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.notifications.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: RefreshIndicator(
        color: TColor.mainColor,
        onRefresh: () async {
          await context.read<NotificationsCubit>().getAllNotifications();
        },
        child: Column(
          children: [
            Expanded(
              child: BlocBuilder<NotificationsCubit, NotificationsState>(
                builder: (context, state) {
                  if (state is NotificationsLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (state is NotificationsSuccess) {
                    final notifications =
                        context.watch<NotificationsCubit>().notificationDataAll;
                    if (notifications == null || notifications.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.notifications_off_outlined,
                              size: 48.w,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16.h),
                            CustomText(
                              text: AppStrings.notifications.tr(),
                              fontSize: 16,
                              textAlign: TextAlign.center,
                              fontW: FontWeight.w500,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                      );
                    }
                    return BlocConsumer<NotificationsCubit, NotificationsState>(
                      listener: (context, state) {
                        if (state is NotificationsLoadingMore) {
                          context.read<NotificationsCubit>().isLoadingMore =
                              true;
                        } else if (state is NotificationsSuccess) {
                          context.read<NotificationsCubit>().isLoadingMore =
                              false;
                        }
                      },
                      builder: (context, state) {
                        return NotificationListener<ScrollNotification>(
                          onNotification: (ScrollNotification scrollInfo) {
                            final threshold =
                                0.9 * scrollInfo.metrics.maxScrollExtent;
                            if (!context
                                    .read<NotificationsCubit>()
                                    .isLoadingMore &&
                                scrollInfo.metrics.pixels >= threshold &&
                                context
                                    .read<NotificationsCubit>()
                                    .hasMoreData) {
                              context
                                  .read<NotificationsCubit>()
                                  .getAllNotifications(
                                    page: context
                                            .read<NotificationsCubit>()
                                            .page +
                                        1,
                                    isFirst: false,
                                  );
                            }
                            return true;
                          },
                          child: Stack(
                            children: [
                              ListView.separated(
                                physics: const AlwaysScrollableScrollPhysics(),
                                padding: EdgeInsetsDirectional.symmetric(
                                  horizontal: 20.w,
                                  vertical: 16.h,
                                ),
                                itemCount: notifications.length +
                                    (context
                                                .read<NotificationsCubit>()
                                                .hasMoreData ==
                                            true
                                        ? 1
                                        : 0),
                                separatorBuilder: (context, index) =>
                                    SizedBox(height: 12.h),
                                itemBuilder: (context, index) {
                                  if (index < notifications.length) {
                                    final notification = notifications[index];
                                    return StatefulBuilder(
                                      builder: (context, setState) {
                                        return MouseRegion(
                                          onEnter: (_) => setState(() {}),
                                          onExit: (_) => setState(() {}),
                                          child: AnimatedContainer(
                                            duration: const Duration(
                                                milliseconds: 200),
                                            decoration: BoxDecoration(
                                              color: notification.isRead == 0
                                                  ? TColor.mainColor
                                                      .withOpacity(0.05)
                                                  : Colors.transparent,
                                              borderRadius:
                                                  BorderRadius.circular(12.r),
                                              border: Border.all(
                                                color: Colors.grey
                                                    .withOpacity(0.2),
                                                width: 1,
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.08),
                                                  blurRadius: 12,
                                                  offset: const Offset(0, 4),
                                                  spreadRadius: 2,
                                                ),
                                              ],
                                            ),
                                            child: Material(
                                              color: Colors.transparent,
                                              child: InkWell(
                                                borderRadius:
                                                    BorderRadius.circular(12.r),
                                                splashColor: TColor.mainColor
                                                    .withOpacity(0.1),
                                                highlightColor: TColor.mainColor
                                                    .withOpacity(0.05),
                                                onTap: () {
                                                  if (notification.isRead ==
                                                      0) {
                                                    setState(() {
                                                      notifications[index] =
                                                          notification.copyWith(
                                                              isRead: 1);
                                                    });
                                                  }
                                                  // Debug info removed
                                                  showDialog(
                                                      barrierDismissible: true,
                                                      context: context,
                                                      builder: (context) {
                                                        return AlertDialog(
                                                          contentPadding:
                                                              EdgeInsets.only(
                                                                  top: 32.h,
                                                                  left: 22.w,
                                                                  right: 22.w,
                                                                  bottom: 26.h),
                                                          backgroundColor:
                                                              TColor.white,
                                                          content: SizedBox(
                                                            width: Provider.of<NotificationsCubit>(
                                                                            context,
                                                                            listen:
                                                                                false)
                                                                        .notificationDataAll![
                                                                            index]
                                                                        .notifications_type ==
                                                                    "tracking"
                                                                ? 500.w
                                                                : 350.w,
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                Column(
                                                                  children: [
                                                                    CustomText(
                                                                      text: Provider.of<NotificationsCubit>(context, listen: false)
                                                                              .notificationDataAll![index]
                                                                              .title ??
                                                                          '',
                                                                      fontSize:
                                                                          18,
                                                                      textAlign:
                                                                          TextAlign
                                                                              .center,
                                                                      fontW: FontWeight
                                                                          .w600,
                                                                      color: TColor
                                                                          .black,
                                                                    ),
                                                                    CustomText(
                                                                      text: Provider.of<NotificationsCubit>(context, listen: false)
                                                                              .notificationDataAll![index]
                                                                              .body ??
                                                                          '',
                                                                      fontSize:
                                                                          18,
                                                                      maxLine:
                                                                          2,
                                                                      textAlign:
                                                                          TextAlign
                                                                              .center,
                                                                      fontW: FontWeight
                                                                          .w600,
                                                                      color: TColor
                                                                          .black,
                                                                    ),
                                                                    10.verticalSpace,
                                                                    RowDetailsWidget(
                                                                      leadingText:
                                                                          'الوقت:',
                                                                      trailingText: CustomDateTime.formatTimeDifference(
                                                                          context,
                                                                          DateTime.now().difference(context
                                                                              .watch<NotificationsCubit>()
                                                                              .notificationDataAll![index]
                                                                              .createdAt!)),
                                                                    ),
                                                                    10.verticalSpace,
                                                                  ],
                                                                ),
                                                                Provider.of<NotificationsCubit>(context,
                                                                                listen: false)
                                                                            .notificationDataAll![index]
                                                                            .notifications_type ==
                                                                        "tracking"
                                                                    ? CustomButton(
                                                                        text:
                                                                            'تتبع',
                                                                        onTap:
                                                                            () {
                                                                          Navigator.pushNamed(
                                                                              context,
                                                                              PathRouteName.openTripScreen);
                                                                        },
                                                                        height:
                                                                            45,
                                                                        radius:
                                                                            15,
                                                                        borderColor:
                                                                            TColor.mainColor,
                                                                        bgColor:
                                                                            TColor.mainColor,
                                                                      )
                                                                    : const SizedBox(),
                                                                Provider.of<NotificationsCubit>(context, listen: false)
                                                                            .notificationDataAll![
                                                                                index]
                                                                            .notifications_type ==
                                                                        "tracking"
                                                                    ? const Sbox(
                                                                        h: 20)
                                                                    : const SizedBox(),
                                                                CustomButton(
                                                                  text: 'أغلق',
                                                                  onTap: () {
                                                                    Navigator.pop(
                                                                        context);
                                                                  },
                                                                  height: 45,
                                                                  radius: 15,
                                                                  borderColor:
                                                                      TColor
                                                                          .mainColor,
                                                                  bgColor: TColor
                                                                      .mainColor,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        );
                                                      });
                                                },
                                                child: AnimatedContainer(
                                                  duration: const Duration(
                                                      milliseconds: 200),
                                                  padding: EdgeInsets.all(8.r),
                                                  child: Row(
                                                    children: [
                                                      Container(
                                                        width: 50.w,
                                                        height: 50.w,
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      8.r),
                                                          image:
                                                              DecorationImage(
                                                            image: AssetImage(
                                                                'assets/images/logo.png'),
                                                            fit: BoxFit.contain,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 12.w),
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              notification
                                                                      .title ??
                                                                  '',
                                                              style: TextStyle(
                                                                fontSize: 16.sp,
                                                                fontWeight: notification
                                                                            .notifications_type ==
                                                                        "0"
                                                                    ? FontWeight
                                                                        .bold
                                                                    : FontWeight
                                                                        .normal,
                                                              ),
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                            ),
                                                            SizedBox(
                                                                height: 4.h),
                                                            Text(
                                                              notification
                                                                      .body ??
                                                                  '',
                                                              style: TextStyle(
                                                                fontSize: 14.sp,
                                                                color: Colors
                                                                    .grey[600],
                                                              ),
                                                              maxLines: 2,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .end,
                                                        children: [
                                                          if (notification
                                                                  .isRead ==
                                                              0)
                                                            Container(
                                                              width: 8.w,
                                                              height: 8.w,
                                                              decoration:
                                                                  const BoxDecoration(
                                                                shape: BoxShape
                                                                    .circle,
                                                                color: TColor
                                                                    .mainColor,
                                                              ),
                                                            ),
                                                          SizedBox(height: 4.h),
                                                          CustomText(
                                                            text: CustomDateTime
                                                                .formatTimeDifference(
                                                              context,
                                                              DateTime.now()
                                                                  .difference(
                                                                DateTime.parse(
                                                                    notification
                                                                        .createdAt
                                                                        .toString()),
                                                              ),
                                                            ),
                                                            fontSize: 12,
                                                            textAlign:
                                                                TextAlign.end,
                                                            fontW:
                                                                FontWeight.w400,
                                                            color: Colors.grey,
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  } else {
                                    return const SizedBox.shrink();
                                  }
                                },
                              ),
                              if (state is NotificationsLoadingMore)
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  child: Container(
                                    color: Colors.white.withOpacity(0.9),
                                    padding: EdgeInsets.all(8.r),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 20.w,
                                          height: 20.w,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2.w,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    TColor.mainColor),
                                          ),
                                        ),
                                        SizedBox(width: 12.w),
                                        CustomText(
                                          text: 'جاري تحميل المزيد...',
                                          fontSize: 14,
                                          textAlign: TextAlign.center,
                                          fontW: FontWeight.w500,
                                          color: TColor.black,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    );
                  } else if (state is NotificationsError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48.w,
                            color: Colors.red,
                          ),
                          SizedBox(height: 16.h),
                          CustomText(
                            text: state.message,
                            fontSize: 16,
                            textAlign: TextAlign.center,
                            fontW: FontWeight.w500,
                            color: Colors.grey,
                          ),
                        ],
                      ),
                    );
                  }
                  // Default state (NotificationsInitial or any other state)
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
