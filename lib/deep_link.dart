// import 'package:busaty_parents/config/global_variable.dart';
// import 'package:busaty_parents/notification_service/utils/logger.dart';
// import 'package:busaty_parents/notification_service/notification_manager.dart';

// /// Initialize FCM using the new unified notification system
// /// This function is kept for backward compatibility but now delegates to NotificationManager
// Future<void> initializeFCM() async {
//   try {
//     Logger.i(
//         'Legacy FCM initialization called - delegating to NotificationManager');

//     // Check if the new notification system is already initialized
//     if (NotificationManager.isInitialized) {
//       Logger.i(
//           'NotificationManager already initialized, skipping duplicate initialization');

//       // Just update the global FCM token variable
//       final token = await NotificationManager.getFCMToken();
//       if (token != null) {
//         initializeFCMToken();
//         Logger.i('FCM token updated from NotificationManager');
//       } else {
//         Logger.w('No FCM token available from NotificationManager');
//       }

//       return;
//     }

//     // If not initialized, initialize the new notification system
//     Logger.i('Initializing NotificationManager from legacy FCM call');
//     await NotificationManager.initialize();

//     // Get FCM token and update global variable
//     final token = await NotificationManager.getFCMToken();
//     if (token != null) {
//       initializeFCMToken();
//       Logger.i('FCM token obtained and cached via NotificationManager');
//     } else {
//       Logger.w('Failed to obtain FCM token via NotificationManager');
//     }
//   } catch (e) {
//     Logger.e('Error in legacy FCM initialization: $e');
//     // Don't rethrow - app should continue even if notifications fail
//   }
// }
