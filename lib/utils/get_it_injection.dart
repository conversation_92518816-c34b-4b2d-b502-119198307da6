import 'package:get_it/get_it.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../services/ad_mob_service.dart';
import 'navigation_helper.dart';

final getIt = GetIt.instance;

Future<void> init() async {
  // Initialize AdMob
  final initAdFuture = MobileAds.instance.initialize();
  final adMobService = AdMobService(initAdFuture);

  getIt.registerSingleton<AdMobService>(adMobService);

  //! ----------- app -----------
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton<SharedPreferences>(() => sharedPreferences);

  // Note: Old notification services removed - now using NotificationManager
  // getIt.registerLazySingleton<FCMNotificationService>(() => FCMNotificationService( FirebaseMessaging.instance),);
  // getIt.registerLazySingleton<LocalNotificationService>(() => LocalNotificationService(FlutterLocalNotificationsPlugin()),);

  getIt.registerSingleton<NavHelper>(NavHelper());
}
